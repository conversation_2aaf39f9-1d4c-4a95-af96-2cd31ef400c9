package com.geeksec.alarmprocessor.pipeline.function;

import com.geeksec.alarmprocessor.config.AlarmProcessorConfig;
import com.geeksec.alarmprocessor.model.Alarm;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.streaming.util.KeyedOneInputStreamOperatorTestHarness;
import org.apache.flink.streaming.util.ProcessFunctionTestHarnesses;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 攻击链分析功能测试类
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public class AttackChainAnalysisFunctionTest {
    
    private static final Logger log = LoggerFactory.getLogger(AttackChainAnalysisFunctionTest.class);
    
    private AttackChainAnalysisFunction attackChainFunction;
    private AlarmProcessorConfig config;
    private KeyedOneInputStreamOperatorTestHarness<String, Alarm, Alarm> testHarness;
    
    @BeforeEach
    void setUp() throws Exception {
        log.info("初始化攻击链分析功能测试");
        
        // 创建测试配置
        config = new AlarmProcessorConfig();
        config.setCorrelationWindowMs(300000L); // 5分钟关联窗口
        config.setMinEventsForChain(2);
        config.setAttackChainMaxCacheSize(1000);
        
        // 创建攻击链分析功能
        attackChainFunction = new AttackChainAnalysisFunction(config);
        
        // 创建测试工具
        testHarness = ProcessFunctionTestHarnesses.forKeyedProcessFunction(
                attackChainFunction,
                Alarm::getAttackChainKey,
                TypeInformation.of(String.class)
        );
        
        testHarness.open();
    }
    
    @Test
    void testSingleAlarmProcessing() throws Exception {
        log.info("测试单个告警处理");
        
        // 创建测试告警
        Alarm alarm = createTestAlarm("alarm-001", "端口扫描", "*************", "********");
        
        // 处理告警
        testHarness.processElement(alarm, 1000L);
        
        // 验证输出
        List<Alarm> output = testHarness.extractOutputValues();
        assertEquals(1, output.size());
        
        Alarm processedAlarm = output.get(0);
        assertNotNull(processedAlarm);
        assertTrue(processedAlarm.getProcessingStatus().getIsAttackChainAnalyzed());
        
        log.info("单个告警处理测试通过");
    }
    
    @Test
    void testAttackChainCorrelation() throws Exception {
        log.info("测试攻击链关联分析");
        
        // 创建相关的告警序列
        Alarm alarm1 = createTestAlarm("alarm-001", "端口扫描", "*************", "********");
        Alarm alarm2 = createTestAlarm("alarm-002", "暴力破解", "*************", "********");
        Alarm alarm3 = createTestAlarm("alarm-003", "WebShell", "*************", "********");
        
        // 设置相同的攻击链键
        String chainKey = "*************->********";
        alarm1.setAttackChainKey(chainKey);
        alarm2.setAttackChainKey(chainKey);
        alarm3.setAttackChainKey(chainKey);
        
        // 按时间顺序处理告警
        testHarness.processElement(alarm1, 1000L);
        testHarness.processElement(alarm2, 2000L);
        testHarness.processElement(alarm3, 3000L);
        
        // 验证输出
        List<Alarm> output = testHarness.extractOutputValues();
        assertEquals(3, output.size());
        
        // 验证攻击链信息
        Alarm lastAlarm = output.get(2);
        assertNotNull(lastAlarm.getAttackChainInfo());
        assertNotNull(lastAlarm.getAttackChainInfo().getChainId());
        assertEquals(3, lastAlarm.getAttackChainInfo().getRelatedAlarms().size());
        assertTrue(lastAlarm.getAttackChainInfo().getCorrelationScore() > 0);
        
        log.info("攻击链关联分析测试通过");
    }
    
    @Test
    void testIsolatedAlarmHandling() throws Exception {
        log.info("测试孤立告警处理");
        
        // 创建孤立告警
        Alarm isolatedAlarm = createTestAlarm("isolated-001", "异常流量", "192.168.1.200", "10.0.0.2");
        
        // 处理告警
        testHarness.processElement(isolatedAlarm, 1000L);
        
        // 验证输出
        List<Alarm> output = testHarness.extractOutputValues();
        assertEquals(1, output.size());
        
        Alarm processedAlarm = output.get(0);
        assertTrue(processedAlarm.getProcessingStatus().getIsAttackChainAnalyzed());
        // 孤立告警不应该有攻击链信息
        assertNull(processedAlarm.getAttackChainInfo());
        
        log.info("孤立告警处理测试通过");
    }
    
    @Test
    void testAttackChainStageProgression() throws Exception {
        log.info("测试攻击链阶段进展");
        
        // 创建模拟攻击链的告警序列
        List<Alarm> attackSequence = createAttackSequence();
        String chainKey = "attacker->target";
        
        // 处理攻击序列
        long timestamp = 1000L;
        for (Alarm alarm : attackSequence) {
            alarm.setAttackChainKey(chainKey);
            testHarness.processElement(alarm, timestamp);
            timestamp += 1000L;
        }
        
        // 验证输出
        List<Alarm> output = testHarness.extractOutputValues();
        assertEquals(attackSequence.size(), output.size());
        
        // 验证阶段进展
        for (int i = 0; i < output.size(); i++) {
            Alarm alarm = output.get(i);
            assertTrue(alarm.getProcessingStatus().getIsAttackChainAnalyzed());
            
            if (i > 0) { // 第一个告警可能没有攻击链信息
                if (alarm.getAttackChainInfo() != null) {
                    assertNotNull(alarm.getAttackChainInfo().getChainStage());
                    assertTrue(alarm.getAttackChainInfo().getRelatedAlarms().size() >= i + 1);
                }
            }
        }
        
        log.info("攻击链阶段进展测试通过");
    }
    
    /**
     * 创建测试告警
     */
    private Alarm createTestAlarm(String alarmId, String alarmType, String srcIp, String dstIp) {
        return Alarm.builder()
                .alarmId(alarmId)
                .alarmType(alarmType)
                .alarmName("测试告警：" + alarmType)
                .srcIp(srcIp)
                .dstIp(dstIp)
                .srcPort(12345)
                .dstPort(80)
                .protocol("TCP")
                .description("这是一个测试告警：" + alarmType)
                .detectorName("TestDetector")
                .confidence(0.85)
                .eventTimestamp(LocalDateTime.now())
                .attackChainKey(srcIp + "->" + dstIp)
                .processingStatus(Alarm.ProcessingStatus.builder()
                        .isDeduplicated(true)
                        .isFormatted(true)
                        .isAttackChainAnalyzed(false)
                        .processingErrors(new ArrayList<>())
                        .build())
                .build();
    }
    
    /**
     * 创建攻击序列
     */
    private List<Alarm> createAttackSequence() {
        List<Alarm> sequence = new ArrayList<>();
        
        sequence.add(createTestAlarm("recon-001", "端口扫描", "*************", "********"));
        sequence.add(createTestAlarm("exploit-001", "漏洞利用", "*************", "********"));
        sequence.add(createTestAlarm("install-001", "恶意软件安装", "*************", "********"));
        sequence.add(createTestAlarm("c2-001", "C2通信", "*************", "********"));
        sequence.add(createTestAlarm("action-001", "数据窃取", "*************", "********"));
        
        return sequence;
    }
    
    void tearDown() throws Exception {
        if (testHarness != null) {
            testHarness.close();
        }
        log.info("攻击链分析功能测试清理完成");
    }
}
