package com.geeksec.alarmprocessor.pipeline.processor;

import com.geeksec.alarmprocessor.model.AlarmType;
import com.geeksec.alarmprocessor.pipeline.processor.impl.*;
import com.geeksec.alarmprocessor.pipeline.processor.impl.AdditionalProcessors.*;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 告警类型处理器工厂
 * 负责创建和管理各种告警类型的处理器
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class AlarmTypeProcessorFactory implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 处理器缓存 */
    private static final Map<AlarmType, AlarmTypeProcessor> PROCESSOR_CACHE = new HashMap<>();
    
    static {
        // 初始化所有处理器
        initializeProcessors();
    }
    
    /**
     * 初始化所有处理器
     */
    private static void initializeProcessors() {
        try {
            // 挖矿相关处理器
            registerProcessor(new MiningVirusProcessor());
            registerProcessor(new MiningConnectionProcessor());
            
            // 扫描相关处理器
            registerProcessor(new ScanBehaviorProcessor());
            registerProcessor(new PenetrationToolProcessor());
            
            // 远控木马处理器
            registerProcessor(new RemoteTrojanProcessor());
            registerProcessor(new IllegalExternalConnectionProcessor());
            
            // 隧道相关处理器
            registerProcessor(new CovertTunnelProcessor());
            registerProcessor(new DNSTunnelProcessor());
            
            // WebShell处理器
            registerProcessor(new WebShellProcessor());
            
            // C2行为处理器
            registerProcessor(new StandardRemoteControlC2Processor());
            registerProcessor(new UnknownRemoteControlProtocolProcessor());
            
            // 证书相关处理器
            registerProcessor(new CertificateAnomalyProcessor());
            
            // 通用处理器
            registerProcessor(new DefaultAlarmProcessor());
            
            log.info("告警类型处理器初始化完成，共注册 {} 个处理器", PROCESSOR_CACHE.size());
            
        } catch (Exception e) {
            log.error("初始化告警类型处理器失败", e);
        }
    }
    
    /**
     * 注册处理器
     */
    private static void registerProcessor(AlarmTypeProcessor processor) {
        PROCESSOR_CACHE.put(processor.getSupportedType(), processor);
        log.debug("注册告警处理器: {}", processor.getSupportedType().getDisplayName());
    }
    
    /**
     * 根据告警类型获取对应的处理器
     * 
     * @param alarmType 告警类型
     * @return 对应的处理器，如果没有找到则返回默认处理器
     */
    public static AlarmTypeProcessor getProcessor(AlarmType alarmType) {
        AlarmTypeProcessor processor = PROCESSOR_CACHE.get(alarmType);
        
        if (processor == null) {
            log.warn("未找到告警类型 {} 的专用处理器，使用默认处理器", alarmType.getDisplayName());
            processor = PROCESSOR_CACHE.get(AlarmType.UNKNOWN);
        }
        
        return processor;
    }
    
    /**
     * 根据告警类型名称获取对应的处理器
     * 
     * @param alarmTypeName 告警类型名称
     * @return 对应的处理器
     */
    public static AlarmTypeProcessor getProcessor(String alarmTypeName) {
        AlarmType alarmType = AlarmType.fromDisplayName(alarmTypeName);
        return getProcessor(alarmType);
    }
    
    /**
     * 检查是否支持指定的告警类型
     * 
     * @param alarmType 告警类型
     * @return 是否支持
     */
    public static boolean isSupported(AlarmType alarmType) {
        return PROCESSOR_CACHE.containsKey(alarmType);
    }
    
    /**
     * 获取所有支持的告警类型
     * 
     * @return 支持的告警类型数组
     */
    public static AlarmType[] getSupportedTypes() {
        return PROCESSOR_CACHE.keySet().toArray(new AlarmType[0]);
    }
    
    /**
     * 获取处理器数量
     * 
     * @return 处理器数量
     */
    public static int getProcessorCount() {
        return PROCESSOR_CACHE.size();
    }
    
    /**
     * 清理处理器缓存（主要用于测试）
     */
    public static void clearCache() {
        PROCESSOR_CACHE.clear();
    }
    
    /**
     * 重新初始化处理器（主要用于测试）
     */
    public static void reinitialize() {
        clearCache();
        initializeProcessors();
    }
}
