package com.geeksec.alarmprocessor.pipeline.processor.impl;

import com.geeksec.alarmprocessor.model.Alarm;
import com.geeksec.alarmprocessor.model.AlarmType;
import com.geeksec.alarmprocessor.pipeline.processor.AbstractAlarmTypeProcessor;
import com.geeksec.common.knowledge.KnowledgeBaseClient;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 默认告警处理器
 * 用于处理未知类型的告警
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class DefaultAlarmProcessor extends AbstractAlarmTypeProcessor {
    
    private static final long serialVersionUID = 1L;
    
    @Override
    public AlarmType getSupportedType() {
        return AlarmType.UNKNOWN;
    }
    
    @Override
    public List<Alarm.DetectionReason> generateReasonAnalysis(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
        List<Alarm.DetectionReason> reasons = new ArrayList<>();
        
        // 通用检测原因
        reasons.add(createDetectionReason(
                "异常行为检测",
                "检测到可疑的网络行为模式",
                "正常网络行为",
                alarm.getDescription() != null ? alarm.getDescription() : "未知异常",
                "正常行为模式",
                5
        ));
        
        // 如果有威胁类型信息
        if (alarm.getThreatType() != null) {
            reasons.add(createDetectionReason(
                    "威胁类型匹配",
                    "检测到已知威胁类型的特征",
                    "正常流量特征",
                    alarm.getThreatType(),
                    "合法流量特征",
                    6
            ));
        }
        
        return reasons;
    }
    
    @Override
    public Alarm.HandlingSuggestions generateHandlingSuggestions(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
        return Alarm.HandlingSuggestions.builder()
                .immediateActions(Arrays.asList(
                        "分析告警详情，确认威胁性质",
                        "检查相关系统和网络状态",
                        "必要时隔离可疑主机或网络连接"
                ))
                .investigationSteps(Arrays.asList(
                        "收集相关日志和证据",
                        "分析攻击模式和影响范围",
                        "确认是否为误报",
                        "评估安全风险等级"
                ))
                .preventiveMeasures(Arrays.asList(
                        "更新安全策略和规则",
                        "加强网络监控",
                        "定期进行安全评估",
                        "提升安全防护能力"
                ))
                .recoverySteps(Arrays.asList(
                        "修复发现的安全问题",
                        "恢复受影响的服务",
                        "更新安全配置",
                        "持续监控系统状态"
                ))
                .build();
    }
    
    @Override
    public String getAlarmPrinciple(Alarm alarm) {
        if (alarm.getThreatType() != null) {
            return String.format("检测到%s类型的安全威胁，可能对系统安全造成影响。", alarm.getThreatType());
        }
        return "检测到可疑的网络活动或安全事件，需要进一步分析确认。";
    }
    
    @Override
    public String getDetectionPrinciple(Alarm alarm) {
        return "基于安全规则和行为分析模型，识别异常的网络活动和安全事件。";
    }
    
    @Override
    public String getModelId(Alarm alarm) {
        return "99999"; // 默认模型ID
    }
}
