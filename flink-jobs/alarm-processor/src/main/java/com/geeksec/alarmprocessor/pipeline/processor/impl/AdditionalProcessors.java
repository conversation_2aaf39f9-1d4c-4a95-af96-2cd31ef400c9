package com.geeksec.alarmprocessor.pipeline.processor.impl;

import com.geeksec.alarmprocessor.model.Alarm;
import com.geeksec.alarmprocessor.model.AlarmType;
import com.geeksec.alarmprocessor.pipeline.processor.AbstractAlarmTypeProcessor;
import com.geeksec.common.knowledge.KnowledgeBaseClient;

import java.util.*;

/**
 * 其他告警处理器的集合
 * 包含多个简化的告警处理器实现
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public class AdditionalProcessors {
    
    /**
     * 渗透工具指纹处理器
     */
    public static class PenetrationToolProcessor extends AbstractAlarmTypeProcessor {
        private static final long serialVersionUID = 1L;
        
        @Override
        public AlarmType getSupportedType() {
            return AlarmType.PENETRATION_TOOL_FINGERPRINT;
        }
        
        @Override
        public List<Alarm.DetectionReason> generateReasonAnalysis(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
            List<Alarm.DetectionReason> reasons = new ArrayList<>();
            reasons.add(createDetectionReason(
                    "渗透工具指纹检测",
                    "检测到已知渗透工具的网络指纹特征",
                    "正常工具指纹",
                    "渗透工具特征",
                    "合法应用指纹",
                    8
            ));
            return reasons;
        }
        
        @Override
        public Alarm.HandlingSuggestions generateHandlingSuggestions(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
            return Alarm.HandlingSuggestions.builder()
                    .immediateActions(Arrays.asList("阻断可疑IP访问", "检查系统漏洞"))
                    .investigationSteps(Arrays.asList("分析攻击工具类型", "确认攻击意图"))
                    .preventiveMeasures(Arrays.asList("更新安全规则", "加强访问控制"))
                    .recoverySteps(Arrays.asList("修复漏洞", "更新防护策略"))
                    .build();
        }
        
        @Override
        public String getModelId(Alarm alarm) {
            return "99064";
        }
    }
    
    /**
     * 远控木马处理器
     */
    public static class RemoteTrojanProcessor extends AbstractAlarmTypeProcessor {
        private static final long serialVersionUID = 1L;
        
        @Override
        public AlarmType getSupportedType() {
            return AlarmType.REMOTE_TROJAN;
        }
        
        @Override
        public List<Alarm.DetectionReason> generateReasonAnalysis(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
            List<Alarm.DetectionReason> reasons = new ArrayList<>();
            reasons.add(createDetectionReason(
                    "远控木马通信",
                    "检测到远程控制木马的通信行为",
                    "正常网络通信",
                    "远控通信特征",
                    "正常业务通信",
                    9
            ));
            return reasons;
        }
        
        @Override
        public Alarm.HandlingSuggestions generateHandlingSuggestions(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
            return Alarm.HandlingSuggestions.builder()
                    .immediateActions(Arrays.asList("立即隔离受感染主机", "阻断C&C通信"))
                    .investigationSteps(Arrays.asList("分析木马类型", "确认感染范围"))
                    .preventiveMeasures(Arrays.asList("更新防病毒软件", "加强终端防护"))
                    .recoverySteps(Arrays.asList("清除木马程序", "修复系统"))
                    .build();
        }
        
        @Override
        public String getModelId(Alarm alarm) {
            return "99005";
        }
    }
    
    /**
     * 违规外联处理器
     */
    public static class IllegalExternalConnectionProcessor extends AbstractAlarmTypeProcessor {
        private static final long serialVersionUID = 1L;
        
        @Override
        public AlarmType getSupportedType() {
            return AlarmType.ILLEGAL_EXTERNAL_CONNECTION;
        }
        
        @Override
        public List<Alarm.DetectionReason> generateReasonAnalysis(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
            List<Alarm.DetectionReason> reasons = new ArrayList<>();
            reasons.add(createDetectionReason(
                    "违规外联行为",
                    "检测到违反安全策略的外部连接",
                    "合规的网络连接",
                    "违规连接目标",
                    "授权的外部服务",
                    7
            ));
            return reasons;
        }
        
        @Override
        public Alarm.HandlingSuggestions generateHandlingSuggestions(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
            return Alarm.HandlingSuggestions.builder()
                    .immediateActions(Arrays.asList("阻断违规连接", "检查连接原因"))
                    .investigationSteps(Arrays.asList("分析连接目的", "确认是否为恶意行为"))
                    .preventiveMeasures(Arrays.asList("更新网络策略", "加强出口控制"))
                    .recoverySteps(Arrays.asList("修复违规问题", "更新安全配置"))
                    .build();
        }
        
        @Override
        public String getModelId(Alarm alarm) {
            return "99009";
        }
    }
    
    /**
     * 隐蔽隧道处理器
     */
    public static class CovertTunnelProcessor extends AbstractAlarmTypeProcessor {
        private static final long serialVersionUID = 1L;
        
        @Override
        public AlarmType getSupportedType() {
            return AlarmType.COVERT_TUNNEL;
        }
        
        @Override
        public List<Alarm.DetectionReason> generateReasonAnalysis(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
            List<Alarm.DetectionReason> reasons = new ArrayList<>();
            reasons.add(createDetectionReason(
                    "隐蔽隧道通信",
                    "检测到隐蔽隧道通信行为",
                    "正常协议通信",
                    "隧道通信特征",
                    "标准协议通信",
                    8
            ));
            return reasons;
        }
        
        @Override
        public Alarm.HandlingSuggestions generateHandlingSuggestions(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
            return Alarm.HandlingSuggestions.builder()
                    .immediateActions(Arrays.asList("阻断隧道通信", "分析隧道类型"))
                    .investigationSteps(Arrays.asList("确认隧道用途", "分析数据传输内容"))
                    .preventiveMeasures(Arrays.asList("加强协议检测", "更新防护规则"))
                    .recoverySteps(Arrays.asList("关闭隧道", "修复安全配置"))
                    .build();
        }
        
        @Override
        public String getModelId(Alarm alarm) {
            return "99010";
        }
    }
    
    /**
     * DNS隧道处理器
     */
    public static class DNSTunnelProcessor extends AbstractAlarmTypeProcessor {
        private static final long serialVersionUID = 1L;
        
        @Override
        public AlarmType getSupportedType() {
            return AlarmType.DNS_TUNNEL;
        }
        
        @Override
        public List<Alarm.DetectionReason> generateReasonAnalysis(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
            List<Alarm.DetectionReason> reasons = new ArrayList<>();
            reasons.add(createDetectionReason(
                    "DNS隧道通信",
                    "检测到通过DNS协议进行的隐蔽通信",
                    "正常DNS查询",
                    "DNS隧道特征",
                    "标准DNS查询",
                    8
            ));
            return reasons;
        }
        
        @Override
        public Alarm.HandlingSuggestions generateHandlingSuggestions(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
            return Alarm.HandlingSuggestions.builder()
                    .immediateActions(Arrays.asList("阻断可疑DNS查询", "检查DNS配置"))
                    .investigationSteps(Arrays.asList("分析DNS查询模式", "确认数据泄露风险"))
                    .preventiveMeasures(Arrays.asList("加强DNS监控", "限制DNS查询"))
                    .recoverySteps(Arrays.asList("修复DNS配置", "更新安全策略"))
                    .build();
        }
        
        @Override
        public String getModelId(Alarm alarm) {
            return "99091";
        }
    }
    
    /**
     * 标准远程控制C2处理器
     */
    public static class StandardRemoteControlC2Processor extends AbstractAlarmTypeProcessor {
        private static final long serialVersionUID = 1L;
        
        @Override
        public AlarmType getSupportedType() {
            return AlarmType.STANDARD_REMOTE_CONTROL_C2;
        }
        
        @Override
        public List<Alarm.DetectionReason> generateReasonAnalysis(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
            List<Alarm.DetectionReason> reasons = new ArrayList<>();
            reasons.add(createDetectionReason(
                    "标准远程控制C2行为",
                    "检测到使用标准远程控制协议的C2通信",
                    "正常远程控制",
                    "C2通信特征",
                    "合法远程管理",
                    8
            ));
            return reasons;
        }
        
        @Override
        public Alarm.HandlingSuggestions generateHandlingSuggestions(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
            return Alarm.HandlingSuggestions.builder()
                    .immediateActions(Arrays.asList("阻断C2通信", "隔离受控主机"))
                    .investigationSteps(Arrays.asList("分析C2协议类型", "确认控制范围"))
                    .preventiveMeasures(Arrays.asList("限制远程控制协议", "加强网络监控"))
                    .recoverySteps(Arrays.asList("清除C2程序", "恢复系统控制"))
                    .build();
        }
        
        @Override
        public String getModelId(Alarm alarm) {
            return "99165";
        }
    }
    
    /**
     * 未知远程控制协议处理器
     */
    public static class UnknownRemoteControlProtocolProcessor extends AbstractAlarmTypeProcessor {
        private static final long serialVersionUID = 1L;
        
        @Override
        public AlarmType getSupportedType() {
            return AlarmType.UNKNOWN_REMOTE_CONTROL_PROTOCOL;
        }
        
        @Override
        public List<Alarm.DetectionReason> generateReasonAnalysis(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
            List<Alarm.DetectionReason> reasons = new ArrayList<>();
            reasons.add(createDetectionReason(
                    "未知远程控制协议",
                    "检测到未知类型的远程控制协议通信",
                    "已知协议通信",
                    "未知协议特征",
                    "标准协议通信",
                    7
            ));
            return reasons;
        }
        
        @Override
        public Alarm.HandlingSuggestions generateHandlingSuggestions(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
            return Alarm.HandlingSuggestions.builder()
                    .immediateActions(Arrays.asList("阻断未知协议通信", "分析协议特征"))
                    .investigationSteps(Arrays.asList("识别协议类型", "确认通信目的"))
                    .preventiveMeasures(Arrays.asList("更新协议识别规则", "加强未知协议检测"))
                    .recoverySteps(Arrays.asList("阻断恶意协议", "更新防护策略"))
                    .build();
        }
        
        @Override
        public String getModelId(Alarm alarm) {
            return "99169";
        }
    }
    
    /**
     * 证书异常处理器
     */
    public static class CertificateAnomalyProcessor extends AbstractAlarmTypeProcessor {
        private static final long serialVersionUID = 1L;
        
        @Override
        public AlarmType getSupportedType() {
            return AlarmType.CERTIFICATE_ANOMALY;
        }
        
        @Override
        public List<Alarm.DetectionReason> generateReasonAnalysis(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
            List<Alarm.DetectionReason> reasons = new ArrayList<>();
            reasons.add(createDetectionReason(
                    "证书异常",
                    "检测到异常的SSL/TLS证书",
                    "正常证书",
                    "异常证书特征",
                    "合法证书",
                    6
            ));
            return reasons;
        }
        
        @Override
        public Alarm.HandlingSuggestions generateHandlingSuggestions(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
            return Alarm.HandlingSuggestions.builder()
                    .immediateActions(Arrays.asList("验证证书有效性", "检查证书来源"))
                    .investigationSteps(Arrays.asList("分析证书异常原因", "确认安全风险"))
                    .preventiveMeasures(Arrays.asList("更新证书验证规则", "加强证书监控"))
                    .recoverySteps(Arrays.asList("更换异常证书", "修复证书配置"))
                    .build();
        }
        
        @Override
        public String getModelId(Alarm alarm) {
            return "99999";
        }
    }
}
