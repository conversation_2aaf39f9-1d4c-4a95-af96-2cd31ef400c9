package com.geeksec.alarmprocessor.pipeline.function;

import com.geeksec.alarmprocessor.config.AlarmProcessorConfig;
import com.geeksec.alarmprocessor.model.Alarm;
import com.geeksec.alarmprocessor.whitelist.PostgreSQLWhitelistService;
import com.geeksec.alarmprocessor.whitelist.WhitelistService;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.functions.RichFilterFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.metrics.Counter;
import org.apache.flink.metrics.MetricGroup;

/**
 * 白名单过滤函数
 * 过滤掉在白名单中的告警
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class WhitelistFilterFunction extends RichFilterFunction<Alarm> {
    
    private static final long serialVersionUID = 1L;
    
    private final AlarmProcessorConfig config;
    
    /** 白名单服务 */
    private transient WhitelistService whitelistService;
    
    /** 指标计数器 */
    private transient Counter totalAlarms;
    private transient Counter filteredAlarms;
    private transient Counter passedAlarms;
    private transient Counter whitelistErrors;
    
    public WhitelistFilterFunction(AlarmProcessorConfig config) {
        this.config = config;
    }
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        
        // 初始化白名单服务
        if (config.isWhitelistEnabled()) {
            this.whitelistService = new PostgreSQLWhitelistService(config);
            log.info("白名单过滤功能已启用");
        } else {
            log.info("白名单过滤功能已禁用");
        }
        
        // 初始化指标
        initializeMetrics();
        
        log.info("白名单过滤函数初始化完成");
    }
    
    /**
     * 初始化指标
     */
    private void initializeMetrics() {
        MetricGroup metricGroup = getRuntimeContext().getMetricGroup()
                .addGroup("whitelist-filter");
        
        totalAlarms = metricGroup.counter("total_alarms");
        filteredAlarms = metricGroup.counter("filtered_alarms");
        passedAlarms = metricGroup.counter("passed_alarms");
        whitelistErrors = metricGroup.counter("whitelist_errors");
    }
    
    @Override
    public boolean filter(Alarm alarm) throws Exception {
        totalAlarms.inc();
        
        // 如果白名单功能未启用，直接通过
        if (!config.isWhitelistEnabled() || whitelistService == null) {
            passedAlarms.inc();
            return true;
        }
        
        try {
            // 检查是否在白名单中
            boolean isInWhitelist = whitelistService.isInWhitelist(alarm);
            
            if (isInWhitelist) {
                // 在白名单中，过滤掉
                filteredAlarms.inc();
                log.debug("告警 {} 在白名单中，已过滤", alarm.getAlarmId());
                
                // 记录过滤信息到告警对象（用于调试）
                if (alarm.getProcessingStatus() != null) {
                    if (alarm.getProcessingStatus().getProcessingErrors() == null) {
                        alarm.getProcessingStatus().setProcessingErrors(new java.util.ArrayList<>());
                    }
                    alarm.getProcessingStatus().getProcessingErrors().add("告警在白名单中被过滤");
                }
                
                return false; // 过滤掉
            } else {
                // 不在白名单中，通过
                passedAlarms.inc();
                log.debug("告警 {} 不在白名单中，通过过滤", alarm.getAlarmId());
                return true; // 通过
            }
            
        } catch (Exception e) {
            whitelistErrors.inc();
            log.error("白名单检查失败，告警ID: {}, 错误: {}", alarm.getAlarmId(), e.getMessage(), e);
            
            // 异常情况下不过滤告警，避免漏报
            passedAlarms.inc();
            return true;
        }
    }
    
    @Override
    public void close() throws Exception {
        if (whitelistService != null) {
            // 输出统计信息
            log.info("白名单过滤统计: 总计={}, 过滤={}, 通过={}, 错误={}",
                    totalAlarms.getCount(),
                    filteredAlarms.getCount(),
                    passedAlarms.getCount(),
                    whitelistErrors.getCount());
            
            // 输出白名单统计信息
            try {
                WhitelistService.WhitelistStatistics stats = whitelistService.getWhitelistStatistics();
                log.info("白名单统计信息: {}", stats);
            } catch (Exception e) {
                log.warn("获取白名单统计信息失败", e);
            }
        }
        
        super.close();
    }
}
