package com.geeksec.alarmprocessor.pipeline.function;

import com.geeksec.alarmprocessor.config.AlarmProcessorConfig;
import com.geeksec.alarmprocessor.model.Alarm;
import com.geeksec.alarmprocessor.model.AlarmType;
import com.geeksec.alarmprocessor.pipeline.processor.AlarmTypeProcessor;
import com.geeksec.alarmprocessor.pipeline.processor.AlarmTypeProcessorFactory;
import com.geeksec.common.knowledge.KnowledgeBaseClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.metrics.Counter;
import org.apache.flink.metrics.MetricGroup;

import java.util.HashMap;
import java.util.Map;

/**
 * 知识库增强函数
 * 使用知识库服务增强告警信息，并应用告警类型特化处理
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class KnowledgeEnrichmentFunction extends RichMapFunction<Alarm, Alarm> {
    
    private static final long serialVersionUID = 1L;
    
    private final AlarmProcessorConfig config;
    
    /** 知识库客户端 */
    private transient KnowledgeBaseClient knowledgeBaseClient;
    
    /** 指标计数器 */
    private transient Counter totalAlarms;
    private transient Counter enrichedAlarms;
    private transient Counter enrichmentErrors;
    private transient Counter knowledgeBaseErrors;
    
    public KnowledgeEnrichmentFunction(AlarmProcessorConfig config) {
        this.config = config;
    }
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        
        // 初始化知识库客户端
        try {
            this.knowledgeBaseClient = new KnowledgeBaseClient();
            log.info("知识库客户端初始化完成");
        } catch (Exception e) {
            log.error("知识库客户端初始化失败", e);
            throw e;
        }
        
        // 初始化指标
        initializeMetrics();
        
        log.info("知识库增强函数初始化完成");
    }
    
    /**
     * 初始化指标
     */
    private void initializeMetrics() {
        MetricGroup metricGroup = getRuntimeContext().getMetricGroup()
                .addGroup("knowledge-enrichment");
        
        totalAlarms = metricGroup.counter("total_alarms");
        enrichedAlarms = metricGroup.counter("enriched_alarms");
        enrichmentErrors = metricGroup.counter("enrichment_errors");
        knowledgeBaseErrors = metricGroup.counter("knowledge_base_errors");
    }
    
    @Override
    public Alarm map(Alarm alarm) throws Exception {
        totalAlarms.inc();
        
        try {
            // 1. 基础知识库增强
            enrichWithKnowledgeBase(alarm);
            
            // 2. 应用告警类型特化处理
            applyAlarmTypeProcessing(alarm);
            
            // 3. 标记为已增强
            alarm.getProcessingStatus().setIsKnowledgeEnriched(true);
            
            enrichedAlarms.inc();
            log.debug("告警知识库增强完成: {}", alarm.getAlarmId());
            
            return alarm;
            
        } catch (Exception e) {
            enrichmentErrors.inc();
            log.error("告警知识库增强失败: {}, 错误: {}", alarm.getAlarmId(), e.getMessage(), e);
            
            // 添加错误信息到告警
            if (alarm.getProcessingStatus().getProcessingErrors() == null) {
                alarm.getProcessingStatus().setProcessingErrors(new java.util.ArrayList<>());
            }
            alarm.getProcessingStatus().getProcessingErrors().add("知识库增强失败: " + e.getMessage());
            
            // 返回原始告警，避免数据丢失
            return alarm;
        }
    }
    
    /**
     * 使用知识库增强告警信息
     */
    private void enrichWithKnowledgeBase(Alarm alarm) {
        try {
            // 增强IP地理位置信息
            enrichIpGeoLocation(alarm);
            
            // 增强威胁情报信息
            enrichThreatIntelligence(alarm);
            
            // 增强域名信息
            enrichDomainInformation(alarm);
            
            // 增强证书信息
            enrichCertificateInformation(alarm);
            
        } catch (Exception e) {
            knowledgeBaseErrors.inc();
            log.warn("知识库查询失败: {}", e.getMessage());
        }
    }
    
    /**
     * 增强IP地理位置信息
     */
    private void enrichIpGeoLocation(Alarm alarm) {
        try {
            // 增强源IP地理位置
            if (alarm.getSrcIp() != null) {
                Map<String, Object> srcGeoInfo = knowledgeBaseClient.getGeoLocation(alarm.getSrcIp());
                if (srcGeoInfo != null) {
                    addToExtendedProperties(alarm, "src_geo_info", srcGeoInfo);
                }
            }
            
            // 增强目标IP地理位置
            if (alarm.getDstIp() != null) {
                Map<String, Object> dstGeoInfo = knowledgeBaseClient.getGeoLocation(alarm.getDstIp());
                if (dstGeoInfo != null) {
                    addToExtendedProperties(alarm, "dst_geo_info", dstGeoInfo);
                }
            }
            
        } catch (Exception e) {
            log.debug("IP地理位置增强失败: {}", e.getMessage());
        }
    }
    
    /**
     * 增强威胁情报信息
     */
    private void enrichThreatIntelligence(Alarm alarm) {
        try {
            // 根据威胁类型获取威胁情报
            if (alarm.getThreatType() != null) {
                Map<String, Object> threatInfo = knowledgeBaseClient.getThreatIntelligenceByName(alarm.getThreatType());
                if (threatInfo != null) {
                    addToExtendedProperties(alarm, "threat_intelligence", threatInfo);
                }
            }
            
            // 检查IP是否为C2威胁IP
            if (alarm.getDstIp() != null) {
                boolean isC2Threat = knowledgeBaseClient.isC2ThreatIp(alarm.getDstIp());
                addToExtendedProperties(alarm, "is_c2_threat_ip", isC2Threat);
            }
            
            // 检查IP是否为IOC IP
            if (alarm.getDstIp() != null) {
                boolean isIocIp = knowledgeBaseClient.isIocIp(alarm.getDstIp());
                addToExtendedProperties(alarm, "is_ioc_ip", isIocIp);
            }
            
        } catch (Exception e) {
            log.debug("威胁情报增强失败: {}", e.getMessage());
        }
    }
    
    /**
     * 增强域名信息
     */
    private void enrichDomainInformation(Alarm alarm) {
        try {
            String domain = extractDomain(alarm);
            if (domain != null) {
                // 检查是否为恶意域名
                boolean isMalicious = knowledgeBaseClient.isMaliciousDomain(domain);
                addToExtendedProperties(alarm, "is_malicious_domain", isMalicious);
                
                // 获取域名排名信息
                Map<String, Object> domainRanking = knowledgeBaseClient.getDomainRanking(domain);
                if (domainRanking != null) {
                    addToExtendedProperties(alarm, "domain_ranking", domainRanking);
                }
            }
            
        } catch (Exception e) {
            log.debug("域名信息增强失败: {}", e.getMessage());
        }
    }
    
    /**
     * 增强证书信息
     */
    private void enrichCertificateInformation(Alarm alarm) {
        try {
            if (alarm.getCertificateInfo() != null) {
                // 获取证书标签评分
                Map<String, Integer> blackScoreMap = knowledgeBaseClient.getCertificateBlackScoreRemarkMap();
                Map<String, Integer> whiteScoreMap = knowledgeBaseClient.getCertificateWhiteScoreRemarkMap();
                
                addToExtendedProperties(alarm, "cert_black_score_map", blackScoreMap);
                addToExtendedProperties(alarm, "cert_white_score_map", whiteScoreMap);
            }
            
        } catch (Exception e) {
            log.debug("证书信息增强失败: {}", e.getMessage());
        }
    }
    
    /**
     * 应用告警类型特化处理
     */
    private void applyAlarmTypeProcessing(Alarm alarm) {
        try {
            // 确定告警类型
            AlarmType alarmType = AlarmType.fromDisplayName(alarm.getAlarmType());
            
            // 获取对应的处理器
            AlarmTypeProcessor processor = AlarmTypeProcessorFactory.getProcessor(alarmType);
            
            // 应用特化处理
            processor.processAlarm(alarm, knowledgeBaseClient);
            
            log.debug("告警类型特化处理完成: {}, 类型: {}", alarm.getAlarmId(), alarmType.getDisplayName());
            
        } catch (Exception e) {
            log.error("告警类型特化处理失败: {}", e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 添加到扩展属性
     */
    private void addToExtendedProperties(Alarm alarm, String key, Object value) {
        if (alarm.getExtendedProperties() == null) {
            alarm.setExtendedProperties(new HashMap<>());
        }
        alarm.getExtendedProperties().put(key, value);
    }
    
    /**
     * 从告警中提取域名
     */
    private String extractDomain(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object domain = alarm.getExtendedProperties().get("domain");
            if (domain != null) {
                return domain.toString();
            }
        }
        
        // 从会话信息中提取
        if (alarm.getSessionInfo() != null) {
            Object httpDomain = alarm.getSessionInfo().get("http_domain");
            if (httpDomain != null) {
                return httpDomain.toString();
            }
            
            Object sniDomain = alarm.getSessionInfo().get("sni_domain");
            if (sniDomain != null) {
                return sniDomain.toString();
            }
        }
        
        return null;
    }
    
    @Override
    public void close() throws Exception {
        if (knowledgeBaseClient != null) {
            try {
                knowledgeBaseClient.close();
                log.info("知识库客户端已关闭");
            } catch (Exception e) {
                log.warn("关闭知识库客户端失败", e);
            }
        }
        
        log.info("知识库增强统计: 总计={}, 增强={}, 错误={}, 知识库错误={}",
                totalAlarms.getCount(),
                enrichedAlarms.getCount(),
                enrichmentErrors.getCount(),
                knowledgeBaseErrors.getCount());
        
        super.close();
    }
}
