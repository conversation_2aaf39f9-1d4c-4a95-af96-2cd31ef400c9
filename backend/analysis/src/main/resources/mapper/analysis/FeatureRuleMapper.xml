<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.geeksec.analysis.dao.FeatureRuleDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.geeksec.analysis.entity.FeatureRule">
        <id column="id" property="id" />
        <result column="task_id" property="taskId" />
        <result column="rule_id" property="ruleId" />
        <result column="rule_level" property="ruleLevel" />
        <result column="rule_name" property="ruleName" />
        <result column="rule_desc" property="ruleDesc" />
        <result column="rule_state" property="ruleState" />
        <result column="rule_size" property="ruleSize" />
        <result column="total_sum_bytes" property="totalSumBytes" />
        <result column="last_size_time" property="lastSizeTime" />
        <result column="capture_mode" property="captureMode" />
        <result column="rule_json" property="ruleJson" />
        <result column="created_time" property="createdTime" />
        <result column="updated_time" property="updatedTime" />
        <result column="rule_hash" property="ruleHash" />
        <result column="rule_family" property="ruleFamily" />
        <result column="byte_ps" property="bytePs" />
        <result column="save_bytes" property="saveBytes" />
        <result column="pb_drop" property="pbDrop" />
        <result column="pcap_drop" property="pcapDrop" />
        <result column="lib_respond_open" property="libRespondOpen" />
        <result column="lib_respond_lib" property="libRespondLib" />
        <result column="lib_respond_config" property="libRespondConfig" />
        <result column="lib_respond_session_end" property="libRespondSessionEnd" />
        <result column="lib_respond_pkt_num" property="libRespondPktNum" />
        <result column="status" property="status" />
        <result column="rule_type" property="ruleType" />
        <result column="lib_data_so" property="libDataSo" />
        <result column="lib_data_conf" property="libDataConf" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, task_id,rule_id, rule_level, rule_name, rule_desc,rule_state, rule_size, total_sum_bytes, last_size_time, capture_mode,
        rule_json, created_time, updated_time, rule_hash, rule_family, byte_ps, save_bytes, pb_drop,
         pcap_drop, lib_respond_open, lib_respond_lib, lib_respond_config, lib_respond_session_end, lib_respond_pkt_num, status,rule_type,
         lib_data_so,lib_data_conf
    </sql>

    <select id="getMaxRuleId" resultType="java.lang.Integer">
        select rule_id from tb_rule
        order by rule_id desc limit 1
    </select>

    <select id="selectOneByHash" resultMap="BaseResultMap">
        select * from tb_rule where status = 1
        and rule_hash =#{hash} and task_id = #{taskId}
         <if test="id!=null">
             AND id != #{id}
         </if>
         limit 1
    </select>

    <update id="deleteFeatureRules" parameterType="com.geeksec.analysis.entity.condition.FilterDeleteCondition">
        update tb_rule
        set status = 0 ,rule_state = '失效',updated_time = unix_timestamp()

        <where>
            AND task_id = #{taskId}
            <if test="ids != null and ids.size()>0">
               AND id in
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </update>

    <select id="getList" parameterType="com.geeksec.analysis.entity.condition.FeatureRuleSearchCondition"
            resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from tb_rule
        <where>
            AND status = 1 AND task_id = #{taskId}
            <if test="ruleId!=null">
                AND rule_id = #{ruleId}
            </if>
            <if test="ruleState!=null and ruleState!=''">
                AND rule_state = #{ruleState}
            </if>
            <if test="ruleName!=null and ruleName!=''">
                AND rule_name like concat('%', #{ruleName}, '%')
            </if>
            <if test="ruleData!=null and ruleData!=''">
                AND rule_json like concat('%', #{ruleData}, '%')
            </if>
            <!-- 筛选条件 过滤规则 -->
            <if test="ruleTypeFilter!=null and ruleTypeFilter.size()>0">
                AND
                <foreach collection="ruleTypeFilter" item="ruleType" open="(" separator="or" close=")">
                    rule_type like concat('%',#{ruleType}, '%')
                </foreach>
            </if>

        </where>
        <if test="orderField !=null and orderField!='' and sortOrder!=null and sortOrder!=''">
            order by ${orderField} ${sortOrder}
        </if>

    </select>

    <select id="getListForLabel" resultType="com.geeksec.analysis.entity.vo.TagInfoVo">
        select rule_id tagId,rule_name tagText,rule_desc tagExplain,rule_level black_list,0 whiteList
        from tb_rule
        where rule_id in
        <foreach collection="ruleIds" item="ruleId" open="(" separator="," close=")">
            #{ruleId}
        </foreach>
    </select>

    <select id="getListForLabel2" resultType="com.geeksec.analysis.entity.session.SessionTagEntity">
        select rule_id tagId,rule_name tagText,rule_level black_list,0 whiteList
        from tb_rule
        where rule_id in
        <foreach collection="ruleIds" item="ruleId" open="(" separator="," close=")">
            #{ruleId}
        </foreach>
    </select>

    <select id="selectCountByUserId" resultType="java.lang.Long">
        SELECT
            count( id )
        FROM
            tb_rule
        WHERE
        1 = 1
        <if test="ruleName != null and ruleName != ''">
            and rule_name like CONCAT('%',#{ruleName},'%')
        </if>
          AND task_id IN (
            SELECT
                task_id
            FROM
                tb_task_analysis
            WHERE
                user_id = #{userId})
    </select>

    <select id="queryList" resultType="com.geeksec.analysis.entity.FeatureRule">
        SELECT
        *
        FROM
        tb_rule
        WHERE
        1 = 1
          and status = 1
        <if test="ruleName != null and ruleName != ''">
            and rule_name like CONCAT('%',#{ruleName},'%')
        </if>
        AND task_id IN (
        SELECT
        task_id
        FROM
        tb_task_analysis
        WHERE
        user_id = #{userId})
    </select>

</mapper>
