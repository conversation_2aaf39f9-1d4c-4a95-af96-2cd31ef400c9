/*
 Navicat Premium Data Transfer

 Source Server         : 51
 Source Server Type    : MySQL
 Source Server Version : 50742
 Source Host           : **************:23306
 Source Schema         : auth_db

 Target Server Type    : MySQL
 Target Server Version : 50742
 File Encoding         : 65001

 Date: 16/06/2023 14:27:39
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

USE auth_db;

-- ----------------------------
-- Table structure for tb_user
-- ----------------------------
DROP TABLE IF EXISTS `tb_user`;
CREATE TABLE `tb_user`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `username` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '用户名',
  `show_username` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '显示名',
  `password` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '密码，加密存储',
  `phone` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '注册手机号',
  `email` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '注册邮箱',
  `groub_id` int(11) NULL DEFAULT NULL COMMENT '用户群组ID',
  `remarks` varchar(2038) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '备注',
  `web_show` int(2) NULL DEFAULT 1 COMMENT '是否在页面显示 0 不展示 1 为展示',
  `status` int(1) NULL DEFAULT 1 COMMENT '是否启用  0 为启用 ， 1 为不启用',
  `created` datetime(0) NULL DEFAULT NULL,
  `updated` datetime(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_groub_id`(`groub_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户表' ROW_FORMAT = Compact;

-- ----------------------------
-- Records of tb_user,密码是geeksec2023@TS的md5值
-- ----------------------------
INSERT INTO `tb_user` VALUES (1, 'admin', '系统管理员', 'e10adc3949ba59abbe56e057f20f883e', '123456789', '<EMAIL>', 1, '系统管理员', 0, 1, '2022-07-19 14:58:05', '2022-07-19 14:58:05');
INSERT INTO `tb_user` VALUES (2, 'root', '服务器管理员', 'e10adc3949ba59abbe56e057f20f883e', '123456789', '<EMAIL>', 1, '系统管理员', 0, 1, '2022-07-19 14:58:05', '2022-07-19 14:58:05');
-- ----------------------------
-- Table structure for tb_user_groub
-- ----------------------------
DROP TABLE IF EXISTS `tb_user_groub`;
CREATE TABLE `tb_user_groub`  (
  `groub_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '用户群组ID',
  `groub_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '群组名',
  `remarks` varchar(2038) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '备注',
  `status` int(1) NULL DEFAULT 1 COMMENT '是否启用  0 为启用 ， 1 为不启用',
  `created` datetime(0) NULL DEFAULT NULL,
  `updated` datetime(0) NULL DEFAULT NULL,
  PRIMARY KEY (`groub_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户群组表' ROW_FORMAT = Compact;

-- ----------------------------
-- Records of tb_user_groub
-- ----------------------------

-- ----------------------------
-- Table structure for tb_user_remote_key
-- ----------------------------
DROP TABLE IF EXISTS `tb_user_remote_key`;
CREATE TABLE `tb_user_remote_key`  (
  `api_key` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `created_time` datetime(0) NULL DEFAULT NULL
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of tb_user_remote_key
-- ----------------------------

-- ----------------------------
-- Table structure for tb_user_resources
-- ----------------------------
DROP TABLE IF EXISTS `tb_user_resources`;
CREATE TABLE `tb_user_resources`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '资源ID',
  `resources` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '资源名称',
  `type` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '资源类型: 1  url ; 2 页面标签 ;  可以继续扩展',
  `status` int(1) NULL DEFAULT 1 COMMENT '是否启用  0 为启用 ， 1 为不启用',
  `created` datetime(0) NULL DEFAULT NULL,
  `updated` datetime(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '资源表' ROW_FORMAT = Compact;

-- ----------------------------
-- Records of tb_user_resources
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;