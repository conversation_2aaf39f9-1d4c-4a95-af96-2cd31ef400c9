"pro_id","pro_value","pro_name","pro_type","pro_exp","type"
"10","PPP","点对点协议","基础协议","点对点协议（PPP）为在点对点连接上传输多协议数据包提供了一个标准方法。PPP 最初设计是为两个对等节点之间的 IP 流量传输提供一种封装协议。在 TCP-IP 协议集中它是一种用来同步调制连接的数据链路层协议（OSI 模式中的第二层），替代了原来非标准的第二层协议，即 SLIP。除了 IP 以外 PPP 还可以携带其它协议，包括 DECnet 和 Novell 的 Internet 网包交换（IPX）。","2"
"11","MPLS","MPLS协议","基础协议","多协议标签交换（MPLS）是一种用于快速数据包交换和路由的体系，它为网络数据流量提供了目标、路由地址、转发和交换等能力。更特殊的是，它具有管理各种不同形式通信流的机制。","2"
"12","ETHERNET","以太网协议","基础协议","以太网(Ethernet)指的是由Xerox公司创建并由Xerox、Intel和DEC公司联合开发的基带局域网规范，是当今现有局域网采用的最通用的通信协议标准。以太网络使用CSMA/CD（载波监听多路访问及冲突检测）技术，并以10M/S的速率运行在多种类型的电缆上。以太网与IEEE802.3系列标准相类似。","2"
"13","IPv4","IPv4协议","基础协议","IPv4，是互联网协议（Internet Protocol，IP）的第四版，也是第一个被广泛使用，构成现今互联网技术的基础的协议。1981年 Jon Postel 在RFC791中定义了IP，Ipv4可以运行在各种各样的底层网络上，比如端对端的串行数据链路(PPP协议和SLIP协议) ，卫星链路等等。局域网中最常用的是以太网。","2"
"14","TCP","TCP协议","基础协议","TCP（Transmission Control Protocol 传输控制协议）是一种面向连接的、可靠的、基于字节流的传输层通信协议，由IETF的RFC 793定义。在简化的计算机网络OSI模型中，它完成第四层传输层所指定的功能，用户数据报协议（UDP）是同一层内[1]  另一个重要的传输协议。在因特网协议族（Internet protocol suite）中，TCP层是位于IP层之上，应用层之下的中间层。不同主机的应用层之间经常需要可靠的、像管道一样的连接，但是IP层不提供这样的流机制，而是提供不可靠的包交换","2"
"15","UDP","UDP协议","基础协议","UDP 是User Datagram Protocol的简称， 中文名是用户数据报协议，是OSI（Open System Interconnection，开放式系统互联） 参考模型中一种无连接的传输层协议，提供面向事务的简单不可靠信息传送服务，IETF RFC 768是UDP的正式规范。UDP在IP报文的协议号是17。","2"
"16","ICMP_v4","ICMP_v4协议","基础协议","是internet protocol suite的核心协议之一. 网络上的操作系统使用这个协议来发送错误信息来说明一些东西, 比如说, 被请求的服务不可用, 或者主机或路由不能被reach到. icmp还可以被用来转播query messages. icmp跟tcp和udp这样的传输协议不同, 它并不是被设计来在系统间传输数据的, 也不是被终端应用程序经常使用的(除了排错工具, 比如说ping和traceroute). 为internet protocol version 4服务","2"
"17","IPv6","IPv6协议","基础协议","IPv6是Internet Protocol Version 6的缩写，其中Internet Protocol译为“互联网协议”。IPv6是IETF（互联网工程任务组，Internet Engineering Task Force）设计的用于替代现行版本IP协议（IPv4）的下一代IP协议，号称可以为全世界的每一粒沙子编上一个网址","2"
"18","ICMP_v6","ICMP_v6协议","基础协议","是internet protocol suite的核心协议之一. 网络上的操作系统使用这个协议来发送错误信息来说明一些东西, 比如说, 被请求的服务不可用, 或者主机或路由不能被reach到. icmp还可以被用来转播query messages. icmp跟tcp和udp这样的传输协议不同, 它并不是被设计来在系统间传输数据的, 也不是被终端应用程序经常使用的(除了排错工具, 比如说ping和traceroute). 为internet protocol version 6服务","2"
"21","IPPAYLOAD","IP负载","基础协议",,"2"
"22","TCP_PayLoad","TCP负载","基础协议",,"2"
"23","UDP_PayLoad","UDP负载","基础协议",,"2"
"30","AODV","AODV协议","按需路由协议","AODV(Ad hoc On-demand Distance Vector Routing)是一种按需路由协议。当一个节点需要给网络中的其他节点传送信息时，如果没有到达目标节点的路由，则必须先以多播的形式发出RREQ(路由请求)报文。RREQ报文中记录着发起节点和目标节点的网络层地址，邻近节点收到RREQ，首先判断目标节点是否为自己。如果是，则向发起节点发送RREP(路由回应)；如果不是，则首先在路由表中查找是否有到达目标节点的路由，如果有，则向源节点单播RREP，否则继续转发RREQ进行查找。","2"
"31","AOE","AOE协议","网络通信协议","AOE是一种网络通信协议，用来连接存储区域网络(SAN)中的存储设备和服务器。","2"
"32","AOL","美国在线",,"美国在线（American Online），2000年至2009年期间是美国时代华纳的子公司，著名的因特网服务提供商。在2000年美国在线和时代华纳（Time Warner）宣布计划合并，2001年1月11日该交易被联邦贸易委员会（Federal Trade Commission）证实。合并及以后的运作的信息见时代华纳。该企业品牌在世界品牌实验室（World Brand Lab）编制的2006年度《世界品牌500强》排行榜中名列第一百三十九。","2"
"33","AP1394",,,,"2"
"35","ARCNET","ARCNET协议","网络协议","ARCNET是一种安装广泛的局域网（LAN）技术，可采用同轴电缆或光缆线。ARCNET为4项主要的LAN技术之一，其他三项为Ethernet，token ring和FDDI。","2"
"36","ARMAGETRONAD","Armagetronad协议",,,"2"
"37","ARP","地址解析协议",,"地址解析协议，即ARP（Address Resolution Protocol），是根据IP地址获取物理地址的一个TCP/IP协议。主机发送信息时将包含目标IP地址的ARP请求广播到网络上的所有主机，并接收返回消息，以此确定目标的物理地址；收到返回消息后将该IP地址和物理地址存入本机ARP缓存中并保留一定时间，下次请求时直接查询ARP缓存以节约资源。地址解析协议是建立在网络中各个主机互相信任的基础上的，网络上的主机可以自主发送ARP应答消息，其他主机收到应答报文时不会检测该报文的真实性就会将其记入本机ARP缓存；由此攻击者就可以向某一主机发送伪ARP应答报文，使其发送的信息无法到达预期的主机或到达错误的主机，这就构成了一个ARP欺骗。ARP命令可用于查询本机ARP缓存中IP地址和MAC地址的对应关系、添加或删除静态对应关系等。相关协议有RARP、代理ARP。NDP用于在IPv6中代替地址解析协议。","2"
"38","ARUBA","ARUBA无线网络",,,"2"
"39","ASAP","ASAP协议",,,"2"
"40","ASCEND","Ascend协议",,,"2"
"41","ASF","高级串流格式",,"ASF是（Advanced Streaming Format） 高级串流格式的缩写，是 Microsoft 为 Windows 98 所开发的串流多媒体文件格式。同JPG、MPG文件一样，ASF文件也是一种文件类型，但是特别适合在IP网上传输。ASF是微软公司Windows Media的核心，这是一种包含音频、视频、图像以及控制命令脚本的数据格式。这个词汇当前可和 WMA 及 WMV 互换使用。利用ASF文件可以实现点播功能、直播功能以及远程教育，具有本地或网络回放、可扩充的媒体类型等优点","2"
"42","ASTERIX","ASTERIX",,"ASTERIX——all purpose structured eurocontrol radar information exchange:是欧洲民航合作组织为了使雷达监视设备与自动化处理系统之间的数据通信标准化而提出的传输规程，已经逐渐成为国际标准的雷达数据交换格式，在ISO/OSI中，ASTERIX是一个表示层和应用层（第6,7层）的标准没有描述具体的链路和传输协议，它支持LAN协议以及TCP/IP.","2"
"43","ATALK",,,,"2"
"44","ATM","ATM协议 ",,"人们一般习惯把电信网分为传输、复用、交换和终端等几个部分。但是随着程控时分交换和时分复用的发展，电信网中的传输、复用和交换这三个部分已越来越紧密地联系在一起了，开始使用传递(transfermode)来统一描述。目前通信网上的传递方式可分为同步传递方式(STM)和异步传递方式(ATM)两种。如ISDN用户线路上的2B+D，以及数字电话网中的数字复用等级等均属于同步传递方式，其特点是在由N路原始信号复合成的时分复用信号中，各路原始信号都是按一定时间间隔周期性出现，所以只要根据时间就可以确定现在是哪一路的原始信号。而异步传递方式的各路原始信号不一定按照一定时间间隔周期性地出现，因而需要另外附加一个标志来表明某一段信息属于哪一段原始信号。例如采用在信元前附加信头的标志就是异步传递方式。宽带ISDN中ATM信元的信头就是一个例子。","2"
"45","ATMTCP","ATM-TCP协议",,,"2"
"46","AUTO",,,,"2"
"47","AX25","AX25",,"为了协调各种分组无线电设备的功能，国际业余无线电联盟正式公布了专门的通信协议。AX.25作为业余分组网的标准补充了HDIC和OSI模型对无线信道分组网的规定","2"
"48","AX4000","AX4000",,,"2"
"49","AYIYA",,,,"2"
"50","BABEL","Babel路由协议",,"Babel属于距离向量路由协议[6]，该协议不仅支持在无线网络也支持有线网络。与RIP为代表的有线网络协议相比，Babel不再使用跳数来进行最优路径的选择，而是使用“期望传输跳数”Expected Transmission Count（ETC）来计算到达某一节点的跳数。ETC不仅考虑了到达某一节点的跳数信息，还综合考虑了链路带宽、拥塞度等一系列指标来作为选路的依据。然而ETC会由于无线环境的变化和网络节点的加入/退出不断变化，这也就造成路由的不断翻转使得网络难以收敛。因此Babel使用历史感知机制（History-sensitive）来加速网络的收敛，即具有相近ETC值的情况下选择曾经使用过的路径。与OLSR不同，Babel在网络收敛后不会在整个网络范围内进行周期性的路由更新和宣告，只有当节点信息和状态发生变化时才会被动触发更新。","2"
"51","BACNET","bacnet协议 ",,"楼宇自动控制网络数据通讯协议（即： A Data Communication Protocol for Building Automation and Control Networks，简称《BACnet协议》）由美国暖通、空调和制冷工程师协会(ASHRAE ) 组织的 标准项目委员会135P (Standard Project Committee即SPC 135P)历经八年半时间开发的","2"
"52","BACP","带宽分配控制协议 ",,"带宽分配控制协议（Bandwidth Allocation Control Protocol）简称BACP，是带宽分配协议（BAP）在多重链路绑定中对多条链接进行管理。","2"
"53","BANANA","BANANA协议",,,"2"
"54","BAP","带宽分配协议",,,"2"
"55","BAT","batman路由协议",,"批处理文件，在DOS和Windows（任意）系统中，.bat文件是可执行文件，由一系列命令构成，其中可以包含对其他程序的调用。这个文件的每一行都是一条DOS命令（大部分时候就好像我们在DOS提示符下执行的命令行一样），你可以使用DOS下的Edit或者Windows的记事本(notepad)等任何文本文件编辑工具创建和修改批处理文件。","2"
"56","BATADV","Bat_Adv协议",,"是一种工作在ISO-OSI第二层上的路由协议，使用以太帧进行通信。因此支持任何能够发送以太帧的接口。
","2"
"57","BCP","bcp",,"BasicCallProcessing，即基本呼叫处理。呼叫处理是指呼叫操作或连接，并包括呼叫建立和撤消，呼叫传送，增加或减少呼叫终端（多方电话会议）,呼叫转发等。媒体处理是指通使用的PC机和重要的应用业务增长时，这种网络同步特征便显示出它的重要性","2"
"58","BEEP","块可扩展交换协议",,,"2"
"59","BER","基本编码规则",,"基本编码规则，定义在 ITU-T X.209 中，是指在 ASN.1 标准（定义在 ITU-T X.208 中）中描述的数据编码/解码规则。基本的编码规则可能被用于为类型值取得传输语法的规范，使用 ASN.1 指定在推荐 X.208 中定义的。一单个 ASN.1 对象可能有几个等价的 BER 编码。BER 是当前 CryptoAPI 使用的两种编码方法之一。","2"
"60","BFD","双向转发检测",,"BFD能够与相邻系统建立对等关系，然后，每个系统以协商的速率监测来自其他系统的BFD速率。监测速率能够以毫秒级增量设定。当对等系统没有接到预先设定数量的数据包时，它推断BFD保护的软件或硬件基础设施发生故障，不管基础设施是标记交换路径、其他类型的隧道还是交换以太网络。BFD部署在路由器和其他系统的控制平面上。BFD检测到的网络故障可以由转发平面恢复或由控制平面恢复.","2"
"61","BGP","边界网关协议",,"边界网关协议（BGP）是运行于 TCP 上的一种自治系统的路由协议。 BGP 是唯一一个用来处理像因特网大小的网络的协议，也是唯一能够妥善处理好不相关路由域间的多路连接的协议。 BGP 构建在 EGP 的经验之上。 BGP 系统的主要功能是和其他的 BGP 系统交换网络可达信息。网络可达信息包括列出的自治系统（AS）的信息。这些信息有效地构造了 AS 互联的拓朴图并由此清除了路由环路，同时在 AS 级别上可实施策略决策。","2"
"62","BICC","品牌形象分类组合,与承载无关的呼叫控制协议",,"BICC是Bearer Independent Call Control protocol的缩写，即与承载无关的呼叫控制协议，是基于ISUP的信号协议，其主要目的是为了解决呼叫控制和承载控制分离的问题，使呼叫控制信令可在各种网络上承载，是由传统电信网络向综合多业务网络演进的重要的支撑工具。","2"
"63","NDPI_BITBORRENT","Bitborrent协议",,,"2"
"64","BJNP","BJNP协议",,,"2"
"65","BOFL","BOFL协议",,,"2"
"66","DHCP","动态主机配置协议 ",,"DHCP（Dynamic Host Configuration Protocol，动态主机配置协议）是一个局域网的网络协议，使用UDP协议工作， 主要有两个用途：给内部网络或网络服务供应商自动分配IP地址，给用户或者内部网络管理员作为对所有计算机作中央管理的手段，在RFC 2131中有详细的描述。DHCP有3个端口，其中UDP67和UDP68为正常的DHCP服务端口，分别作为DHCP Server和DHCP /的服务端口；546号端口用于DHCPv6 Client，而不用于DHCPv4，是为DHCP failover服务，这是需要特别开启的服务，DHCP failover是用来做“双机热备”的。","2"
"67","BPDU","网桥协议数据单元",,"BPDU是运行STP的交换机之间交换的消息帧。BPDU内包含了STP所需的路径和优先级信息，STP便利用这些信息来确定根桥以及到根桥的路径。","2"
"68","BPQ",,,,"2"
"69","BRDWLK",,,,"2"
"70","BRP","BRP协议",,"BRP协议是为不可靠信道上传送大数据包文件设计的工业协议","2"
"71","BSSAP","Base Station System Application",,"基站系统应用部分,为gsm中A接口上的应用层协议.","2"
"72","BT","BT协议",,"BT协议Bit Torrent（BT）是一种通信协议，又是一种应用程序，广泛用于对等网络通信（P2P）。曾经风靡一时，由于它引起了巨大的流量，因此对因特网的运营、维护和管理都产生了重要的影响。","2"
"73","BT3DS","蓝牙-3DS协议",,,"2"
"74","BTA2DP","音频传输协议",,"A2DP(Advanced Audio Distribution Profile)是蓝牙的音频传输协议，典型应用为蓝牙耳机。A2DP协议的音频数据在ACL Link上传输，这与SCO上传输的语音数据要区别。A2DP不包括远程控制的功能，远程控制的功能参考协议AVRCP。AVDTP则定义了蓝牙设备之间数据流句柄的参数协商，建立和传输过程以及相互交换的信令实体形式，该协议是A2DP框架的基础协议","2"
"75","BTAMP","蓝牙-AMP协议",,"PHY层扩展","2"
"76","BTATT","蓝牙-ATT协议",,,"2"
"77","BTAVCTP","蓝牙-AVCTP协议",," AVCTP协议描述了蓝牙设备间Audio/Video的控制信号交换的格式和机制，它是一个总体的协议，具体的控制信息由其指定的协议(如AVRCP)实现，AVCTP本身只指定控制command和response的总体的格式。","2"
"78","BTAVDTP","蓝牙-AVDTP协议",,"AVDTP(AUDIO/VIDEO DISTRIBUTION TRANSPORT PROTOCOL)是用来描述音频/视频在蓝牙设备间的传输的协议，是A2DP协议的基础协议","2"
"79","BTAVRCP","蓝牙-AVRCPX协议",,"Audio / Video Remote Control Profile音/视频远程控制协议","2"
"80","BTBNEP","蓝牙-BNEP协议",,,"2"
"81","BTDUN","蓝牙-DUN协议",,"DUN提供了通过Bluetooth无线技术接入Internet和其它拨号服务的标准。","2"
"82","BTGNSS","蓝牙-GNSS协议",,,"2"
"83","BTHCI","蓝牙-HCI协议",,"主机控制接口协议","2"
"84","BTHCRP","蓝牙-HCRP协议",,"HCRP定义了如何通过Bluetooth无线链路完成基于驱动程序的打印","2"
"85","BTHFP","蓝牙-HFP协议",,"HFP描述了网关设备如何用于供免提设备拨打和接听呼叫。典型配置如汽车使用手机作为网关设备。在车内，立体声系统用于电话音频，而车内安装的麦克风则用于通话时发送输出音频。HFP还可用于个人计算机在家中或办公环境中作为手机扬声器的情况","2"
"86","BTHID","蓝牙-HID协议",,"HID协议简介 人机接口设备(HID)主要是指一些人与计算机进行交互的设备，如键盘、鼠标、游戏杆等;但是HID设备不一定非要是这些人机交互设备，只要符合HID设备级定义规范要求的都可以认为是HID设备","2"
"87","BTMCAP","蓝牙-MCAP协议",,"多信道适配协议","2"
"88","BTOBEX","对象交换(OBEX)协议",,"OBEX传输协议定义了数据对象和两个设备用来交换这些对象的通信协议。OBEX支持应用程序在Bluetooth协议堆栈及IrDA堆栈上工作。对于Bluetooth设备，仅支持面向连接的OBEX。已使用OBEX开发出三种应用配置文件，即SYNC、FTP和OPP。","2"
"89","BTRFCOMM","蓝牙—RFCOMM协议",,"RFCOMM是一个简单的协议，其中针对9针RS-232串口仿真附加了部分条款.可支持在两个蓝牙设备之间同时保持高达60路的通信连接.RFCOMM的目的是针对如何在两个不同设备上的应用之间保证一条完整的通信路径","2"
"90","BTSAP","蓝牙-SAP协议",,,"2"
"91","BTSDP","蓝牙的SDP协议",,"SDP协议让客户机的应用程序发现存在的服务器应用程序提供的服务以及这些服务的属性。SDP只提供发现服务的机制，不提供使用这些服务的方法。每个蓝牙设备都需要一个SDPService，只做Client的蓝牙设备除外。","2"
"92","BTSMP","蓝牙中的SMP",,"SMP即Security Manage Protocol。是蓝牙用来进行安全管理的，其定义了配对和密钥分发的过程实现。SMP被用在LE-only设备或蓝牙双模设备中。","2"
"93","BTSPP","蓝牙串口协议",,,"2"
"94","BTVDP","蓝牙-VDP协议",,,"2"
"95","BUNDLE","bundle",,".bundle格式的文件是Unix/linux系统中的一种可执行文件。用户可以在终端中使用./***（文件名）.bundle命令使其运行","2"
"96","BVLC","BVLC协议",,,"2"
"97","BZR","bzr工具",,"Bazaar（bzr）是另一个开源的 DVCS（Distributed Version Control System，即分布式版本控制系统），它试图给 SCM（Source Code Management，即源码管理） 的世界里带来一些新的东西。　bzr 是 Canonical 公司支持的一个项目，也就是 Ubuntu Linux 的发行公司。简单来说，bzr 是用 python 编写的，用于版本控制。","2"
"98","C1222",,,,"2"
"99","CALCA",,,,"2"
"100","CAP","CAP原则 ",,"CAP原则又称CAP定理，指的是在一个分布式系统中，Consistency（一致性）、 Availability（可用性）、Partition tolerance（分区容错性），三者不可得兼","2"
"101","CAPWAP","CAPWAP 控制的无线接入点和配置协议",,"CAPWAP是Control And Provisioning of Wireless Access Points Protocol Specification的缩写，意为无线接入点的控制和配置协议。是由IETF（互联网工程任务组）标准化组织于2009年3月定义。","2"
"102","CARP","通用预警协议",,"通用预警协议(即Common Alerting Protocol简写为CAP)是一种简化的标准化XML数据格式，适用于各种网络交换灾害紧急预警与公共预警信息。","2"
"103","CAST","CAST",,,"2"
"104","CATAPULT","CATAPULT协议",,,"2"
"105","CBCP","回叫控制协议",,,"2"
"106","CCID","芯片智能卡接口设备 ",,"CCID（USB Chip/Smart Card Interface Devices-USB芯片智能卡接口设备）标准是由几大国际级IT企业共同制定的一个标准，它提供了一种智能卡读写设备与主机或其它嵌入式主机实现相互通讯的可能。","2"
"107","CCP","CCP协议",,"CCP(CAN Calibration Protocol)是一种基于CAN总线的ECU(Electronic Control Unit)标定协议,已经在许多欧美汽车厂商得到应用,采用CCP协议可以快速而有效地实现对汽车电控单元的标定。","2"
"108","CDP","思科发现协议",,"CDP是Cisco DiscoveryProtocol的缩写，它是由思科公司推出的一种私有的二层网络协议，它能够运行在大部分的思科设备上面。通过运行CDP 协议，思科设备能够在与它们直连的设备之间分享有关操作系统软件版本，以及IP地址，硬件平台等相关信息。","2"
"109","CDPCP","思科发现协议控制协议",,,"2"
"110","CFM","连接故障管理","基础协议","支持OAM功能以保证以太网也能够提供电信级的要求，包括能够对一些业务降级和失败等网络异常错误或者异常问题能够进行及时检测、恢复和管理的功能","2"
"111","CGMP","思科组管理协议",," CGMP主要用来限定只向与IP 组播客户机相连的端口转发 IP 组播数据包。这些客户机自动加入和离开接收IP组播流量的组，交换机根据请求动态改变其转发行为。","2"
"112","CHAP","PPP点对点协议询问握手认证协议 ",,"CHAP全称是PPP（点对点协议）询问握手认证协议 （Challenge Handshake Authentication Protocol）。该协议可通过三次握手周期性的校验对端的身份，可在初始链路建立时，完成时，在链路建立之后重复进行。通过递增改变的标识符和可变的询问值，可防止来自端点的重放攻击，限制暴露于单个攻击的时间。","2"
"113","Cisco_HDLC","高级数据链路控制 ",,"是一个在同步网上传输 数据、面向比特的数据链路层协议，它是由国际标准化组织（ISO）根据IBM公司的SDLC（Synchronous Data Link Control）协议扩展开发而成的。","2"
"114","CIMETRICS",,,,"2"
"115","CIP","CIP",,"CIP是一种为工业应用开发的应用层协议，被deviceNet、ControINet、EtherNet/IP三种网络所采用，因此这三种网络相应地统称为CIP网络。","2"
"116","CIPSAFETY","CIPSAFETY",,"是一系列高度集成的安全服务，此安全服务利用通用工业协议(CIP)网络的下层通讯堆栈，可以将安全数据从数据源传送到目的地。CIP Safety是独立于网络的，最初的版本是用于DeviceNet™上的。现在它已经经过拓展支持EtherNet/IP™，从而带来更高的数据吞吐量、传输距离和灵活性","2"
"117","CLASSICSTUN","CLASSICSTUN",,,"2"
"118","CLIP","Clip",,,"2"
"119","CLNP","网络连接协议",,"CLNP是网络连接协议，可以用于终端系统的网络实体之间或网络层中继系统中。CLNP 使用 NSAP 地址和标题来识别网络设备，就象 IP 一样，CLNP 协议头的校验和提供了一种认证，该认证用于处理 CLNP 数据报是否已正确传输，以及提供了生命周期控制机制 ，该机制限制了数据报停留在在英特网系统中的时间。","2"
"120","CMD","命令提示符",,"cmd是command的缩写.即命令提示符（CMD），是在OS / 2 ， Windows CE与Windows NT平台为基础的操作系统（包括Windows 2000和XP中， Vista中，和Server 2003 ）下的“MS-DOS 方式”。中文版Windows XP 中的命令提示符进一步提高了与DOS 下操作命令的兼容性，用户可以在命令提示符直接输入中文调用文件。","2"
"121","CMP","证书管理协议",,"This document describes the Internet X.509 Public Key Infrastructure(PKI) Certificate Management Protocol (CMP).  Protocol messages are defined for X.509v3 certificate creation and management.CMP provides on-line interactions between PKI components, including an   exchange between a Certification Authority (CA) and a client system.","2"
"122","CMPP","CMPP协议 ",,"CMPP协议 （China Mobile Peer to Peer） 的全称是中国移动通信互联网短信网关接口协议，它是联想亚信公司根据SMMP协议为中国移动量身定做的，是符合中国国情的一个短信协议。","2"
"123","CNIP",,,,"2"
"124","COAP","CoAP",,"CoAP是受限制的应用协议(Constrained Application Protocol)的代名词。在最近几年的时间中，专家们预测会有更多的设备相互连接，而这些设备的数量将远超人类的数量。在这种大背景下，物联网和M2M技术应运而生。虽然对人而言，连接入互联网显得方便容易，但是对于那些微型设备而言接入互联网非常困难。在当前由PC机组成的世界，信息交换是通过TCP和应用层协议HTTP实现的。但是对于小型设备而言，实现TCP和HTTP协议显然是一个过分的要求。为了让小设备可以接入互联网，CoAP协议被设计出来。CoAP是一种应用层协议，它运行于UDP协议之上而不是像HTTP那样运行于TCP之上。CoAP协议非常的小巧，最小的数据包仅为4字节。","2"
"125","COLLECTD","collectd",,"collectd是一个守护(daemon)进程，用来定期收集系统和应用程序的性能指标，同时提供了机制，以不同的方式来存储这些指标值。","2"
"126","COMP","comp",,"可以比较相同驱动器或不同驱动器上的文件以及相同目录或不同目录中的文件","2"
"127","COMPONENTSTATUS",,,,"2"
"128","COPS","公共开放策略服务协议",,"公共开放策略服务（COPS：Common Open Policy Service） 协议是一种简单的查询和响应协议，其主要用于在策略服务器（策略决策点 PDP）与其客户机（策略执行点 PEP）之间交换策略信息。策略客户机的一个典型例子是 RSVP 路由器，它主要行使基于策略的允许控制功能。在每个受控管理域中至少有一个策略服务器。","2"
"129","COSINE","COSINE协议",,,"2"
"130","COTP","面向连接的传输协议",,"COTP 是 OSI 7层协议定义的位于TCP之上的协议。","2"
"131","CPFI","CPFI协议",,,"2"
"132","CPHA","时钟相位",,"能够配置用于选择两种不同的传输协议之一进行数据传输","2"
"133","CSM","兼容性支持模块 ",,"CSM（兼容性支持模块）是BIOS上Boot选项里的一个下拉子项目（一些老的主板上没有此选项），与Secure Boot（安全启动）是并列项。CSM开启使得可以支持UEFI启动和非UEFI启动。若是需要启动传统MBR设备，则需开启CSM。关闭CSM则变成纯UEFI启动，且完全支持安全启动。Secure Boot（安全启动），安全启动仅适用于使用UEFI启动的操作系统。在笔记本的BIOS里，这里更多的是使用Enabled（打开）和Disabled（关闭）两个选项。在电脑自带Windows 8的情况下，Secure Boot（安全启动）默认是Enabled（打开）。从而使得CSM（兼容性支持模块）又默认是Disabled（关闭），进而导致电脑不能启动不完全支持UEFI的设备。要使得电脑能启动不完全支持UEFI的设备，就必须关闭Secure Boot，然后打开CSM。","2"
"134","CUPS","CUPS协议",,"CUPS(Common UNIX Printing System，通用Unix打印系统)是Fedora Core3中支持的打印系统，它主要是使用IPP(Internet Printing Protocol)来管理打印工作及队列，但同时也支持""LPD""(Line Printer Daemon)和""SMB""(Server Message Block)以及AppSocket等通信协议。","2"
"135","CWIDS",,,,"2"
"136","DAP","目录访问服务协议(x.500)",,"属于ISO/ITU-T X.500 协议","2"
"137","DAYTIME","DAYTIME协议",,"DAYTIME协议是基于TCP的应用，是一种有用的调试工具，它的作用是返回当前时间和日期，格式是字符串格式。","2"
"138","DB","D型数据接口连接器",,"D型数据接口连接器，用于连接电子设备（比如：计算机与外设）的接口标准。因形状类似于英文字母D，故得名D型接口","2"
"139","DBUS","消息总线系统",,"DBUS，数据总线，是一个低延迟，低开销，高可用性的ipc机制。","2"
"140","DCCP","数据报拥塞控制协议",,"数据报拥塞控制协议","2"
"141","DCM","数据通信模块",,"数据通信模块DCM是汽车上使用的网络通信功能模块，只要轻轻一按即可联机，使用户享受高速通讯，不须考虑登入时间及传输量，而且DCM在中断时具有自动联机功能，比如车在过隧道的时候。","2"
"142","DCP","数字电位器",,"DCP为""Digitally Controlled Potentiometers"" 的缩写，即数字电位器，它的写次数很容易达到50000次，而机械式电位器的调节次数一般只有几千次，甚至几百次。大多数数字电位器可以通过传统的I2C或SPI接口进行编程，有些器件则采用上/下脉冲计数调节方式。首先，这些电位器对灰尘、污垢和潮湿的环境不敏感，而这些因素对于机械式电位器来说则是致命的。数字电位器几乎能够在任何电子系统中替代老式的机械电位器，而不仅仅是在音频产品。 DCP称为过氧化二异丙苯(dicumyl peroxide)、过氧化二枯茗。分子式C18H22O2，相对分子质量270.37。DCP也是一种国际贸易术语解释通则里第二版本的一个术语，全称Delivered Costs Paid","2"
"143","DDTP","残障通讯计划",,,"2"
"144","DEC",,,,"2"
"145","DECT","数字增强无绳通信",,"数字增强无绳通信(Digital Enhanced Cordless Telecommunications ，DECT) 系统，是由欧洲电信标准协会(European Telecommunications Standards Institute)制定的增强型数字无绳电话标准，是一个开放型的，不断演进的数位通讯标准，主要用于无绳电话系统。可为高用户密度，小范围通信提供话音和数据高质量服务无绳通信的框架。","2"
"146","DHCPFO","DHCPFO协议",,,"2"
"147","DHCPv6","DHCPv6协议",,"DHCPv6是一个用来配置工作在IPv6网络上的IPv6主机所需的IP地址、IP前缀和/或其他配置的网络协议。","2"
"148","DIAMETER","Diameter协议",,"diameter协议最初是作为radius协议的改进或者替代，它是ietf开发的新一代aaa协议（authentication认证，authorization授权，accounting计费）。authentication(认证)用以对用户身份进行确认;authorization(授权) 用以确定用户是否被授权使用某种网络资源;accounting(计费)用以监测用户使用网络资源的状况，可依照检测的记录对用户收费。","2"
"149","DIS","dis协议",,,"2"
"150","DISP","Directory Information Shadowing Protocol",,"DISP是指数码相机机身按键，特指文件信息。在微型计算机原理与接口技术中代表位移量。在MATLAB中的命令，表示只显示结果，不显示变量名。","2"
"151","DISTCC","DISTCC协议 ",,,"2"
"152","DLM3",,,,"2"
"153","DLSW","DLSW",,"DLSw（Data Link Switching，数据链路交换）是APPN（Advanced Peer-to-Peer Networking，高级对等网络）、Implementers Workshop（AIW，实现工作组）开发，用来实现通过TCP/IP承载SNA（System Network Architecture，系统网络结构体系）的一种方法。SNA是IBM在70年代推出的与OSI参考模型对应的网络体系结构。要实现SNA协议跨广域网传输，解决方案之一就是DLSw技术。","2"
"154","DMP","DMP协议",,"通讯协议","2"
"155","DNP3",,,,"2"
"156","DNS","域名系统",,"DNS是域名系统(DomainNameSystem)的缩写，该系统用于命名组织到域层次结构中的计算机和网络服务。域名是由圆点分开一串单词或缩写组成的，每一个域名都对应一个惟一的IP地址，在Internet上域名与IP地址之间是一一对应的，DNS就是进行域名解析的服务器。DNS命名用于Internet等TCP/IP网络中，通过用户友好的名称查找计算机和服务。DNS是因特网的一项核心服务,它作为可以将域名和IP地址相互映射的一个分布式数据库。","2"
"157","DOP","DOP协议",,,"2"
"158","DPNSS","网络信令系统",,,"2"
"159","DSI","dsi",,"数字话音插空(DSI)技术是卫星通信中所采用的一种能以提高信道利用率和扩大信道容量的很有发展前途的新技术。在通话过程中，一方在讲话时，另一方必然在听。也就是说电路总有一个方向是空闲的，况且讲话的一方也有停顿，因此电路中每一方向的利用率不到50%。利用已经占用的电路，在通话过程中的空闲时间，来传送其他话路信号。","2"
"160","DSMCC","数字存储媒体命令和控制扩展协议",,"用于管理 MPEG-1 和 MPEG-2 的数据流，使数据流既可在单机上运行，又可在异构网络（即用类似设备构造但运行不同协议的网络）环境下运行。在 DSM-CC 模型中，服务器和客户器都被认为是 DSM-CC 网络的用户，DSM-CC 定义了一个称为会话和资源管理（Session and Resource Manager，SRM）的实体，用来集中管理网络中的会话和资源","2"
"161","DSP","DSP协议",,"DSP动态调度协议该协议动态地选择间隙传输请求或重传失败请求，这里提出2个不同的间隙选择机制。(1)均衡间隙选择。实时数据和非实时数据以概率P试图在N个间隙的开始进行传输，数据在每个间隙传输的概率P=1/N，如果传输成功，数据就按照这种结构请求明确的间隙进行传输，当冲突发生时，数据利用上面定义的概率选择空闲间隙继续传输。(2)加权间隙选择。构造以下概率组：{β,2β,⋯,Nβ}，其中，β=1/(1+2+⋯+N)","2"
"162","DTCP",,,,"2"
"163","DTLS","DTLS",,"DTLS(Datagram Transport Layer Security)即数据包传输层安全性协议。TLS不能用来保证UDP上传输的数据的安全，因此Datagram TLS试图在现存的TLS协议架构上提出扩展，使之支持UDP，即成为TLS的一个支持数据报传输的版本。DTLS 1.0 基于 TLS 1.1, DTLS 1.2 基于TLS 1.2。","2"
"164","DTP","动态中继协议",,"DTP的英文名为Dynamic Trunk Protocol，是一项动态中继协议。此协议可以让Cisco交换机自动协商指定交换机之间的链路是否形成Trunk。","2"
"165","DTPT",,,,"2"
"166","DUA","目录用户代理",,"目录用户代理（DUA：Directory User Agent）是用于访问一个或多个 DSA 的用户接口程序。DUA包括 whois，查找器（finger）以及提供图形用户界面的相关程序等。","2"
"168","DVBCI","DVB-CI协议",,,"2"
"169","EAP","EAP协议",,"EAP协议是使用可扩展的身份验证协议的简称，全称Extensible Authentication Protocol。是一系列验证方式的集合，设计理念是满足任何链路层的身份验证需求，支持多种链路层认证方式。","2"
"170","EAPOL","基于局域网的扩展认证协议",,"EAP是Extensible Authentication Protocol的缩写，EAPOL就是(EAP OVER LAN )基于局域网的扩展认证协议。 EAPOL是基于802.1X网络访问认证技术发展而来的。","2"
"171","ECHO","应答协议",,"可以基于TCP协议，服务器就在TCP端口7检测有无消息，如果使用UDP协议，基本过程和TCP一样，检测的端口也是7。 是路由也是网络中最常用的数据包，可以通过发送echo包知道当前的连接节点有那些路径，并且通过往返时间能得出路径长度。 希腊神话中，Echo是一个森林女神 。有同名专辑。","2"
"172","ECP","ECP企业管理云平台",,"ECP企业管理云平台（Enterprise Cloud Platform）是中亚网络自主研发的，开创中国企业管理软件定制标准，引领中国IT产业发展趋势的企业管理软件云平台。CAECP采用云计算SAAS平台，利用高速化的互联网传输能力，将数据处理过程从单个的处理器转移到CADC——China Asia Date Center，为软件开发商搭建一个高效、灵活、免费的软件商铺；同时根据需求智能分配计算资源，不仅满足中小企业日益增长的信息化管理要求，同时也为含分公司的大型集团企业提供高效、安全、稳定的一站式专业级软件应用服务。","2"
"173","NDPI_EDONKEY","Edonkey协议",,,"2"
"174","EDP","EDP协议",,"数据（透传）转发协议","2"
"175","EGD","EGD协议",,,"2"
"176","EIGRP","增强内部网关路由线路协议",,"EIGRP:Enhanced Interior Gateway Routing Protocol 即 增强内部网关路由协议。也翻译为 加强型内部网关路由协议。 EIGRP是Cisco公司的私有协议（2013年已经公有化）。 EIGRP结合了链路状态和距离矢量型路由选择协议的Cisco专用协议，采用弥散修正算法（DUAL）来实现快速收敛，可以不发送定期的路由更新信息以减少带宽的占用，支持Appletalk、IP、Novell和NetWare等多种网络层协议。","2"
"177","EISS",,,,"2"
"178","ELCOM","ELCOM协议",,,"2"
"179","ENC","ENC协议",,,"2"
"180","ENIP","新一代业务平台",,"ENIP通过协议模块化等技术，动态支持INAP、SIP、CAP、MAP、WIN-CDMA、RADIUS、Diameter等协议；可以同时接入一个或者多个基础网络（PSTN、GSM、WCDMA、CDMA/CDMA2000、NGN、IMS等），可以支持多种类型的终端，为跨网络的、以用户体验为基础、以用户为中心的业务开展奠定了基础；","2"
"181","ENRP","ENRP协议",,,"2"
"182","ENTTEC","ENTTEC协议",,,"2"
"183","EPL","设备保护级别",,"EPL（Ethernet Private Line）就是以太网专线业务，以太网专线接入业务也可理解为透传，透传的含义——就是用户数据在我们的接入、传送、落地过程中，我们的接入、落地设备和数据所经过的传送网对于用户的数据来说就象一条专线一样，除了VLAN路由配置方式下，单板数据入口处对接入数据进行了VLAN标记的识别并做出是否传送还是丢弃的判择外，用户数据在整个通路中完全透明的传送并交互。简单的理解就是在我们的接入设备中，透传点对点的专线业务类型。","2"
"184","EPMD","EPMD协议",,,"2"
"185","ERF",,,,"2"
"186","ERSPAN","封装远程端口镜像",,"封装远程端口镜像(ERSPAN)是远程端口镜像(RSPAN)的扩展","2"
"187","ESIO",,,,"2"
"188","ESIS","端系统对中间系统(ESIS)协议",,"OSI的路由选择 Routing，OSI 开放系统互连(OSI)环境由包括端系统（用户计算机或主机）和路由器的管理域组成。一个管理域通常使用相同的协议和由同一个中心机构管理。所有在域内的路由选择叫做域内(Intradomain)路由选择，所有在域外连接其它域的路由选择叫做域间(Interdomain)路由选择。域间路由选择涉及连到“不大可信”的环境，可是管理员宁愿手工设置通路，而不愿依靠路由选择协议自动构造域间通路。","2"
"189","ETCH","Etch协议",,"思科宣布推出开源的消息转发协议Etch，该协议允许开发人员整合客户端和服务器应用程序，而不必使用传统的SOAP协议。","2"
"191","ETHERIP","ETHERIP协议",,,"2"
"192","ETV","ETV",,"ETV是网络机顶盒，使用的话，要安装使用无线路由器才可以连接的，设置好无线路由器后可以有线或无线连接。","2"
"193","EVRC","增强型可变速率编解码器",,"增强型可变速率编解码器（Enhanced Variable Rate Codec，EVRC）是由美国电信工业协会（TIA/EIA）为cdma2000系统提出的变速率语音编码技术。EVRC基于松散码激励线性预测（Relaxed Code Excited Linear Prediction，RCELP）编码器技术，并加入了语音激活检测（Voice Activity Detection，VAD）、差错隐藏等技术，对语音信号进行变速率编码从而达到节约带宽、保持语音质量的目的。","2"
"194","EXEC","exec函数族",,"exec函数族，顾名思义，就是一簇函数，他把当前进程映像替换成新的程序文件，而且该程序通常main函数开始执行。","2"
"195","EXPORTED",,,,"2"
"196","FABRICPATH","FabricPath",,"FabricPath（结构路径）是美国Cisco公司在TRILL标准之上做了一些优化而产生的私有协议。","2"
"197","FC","FC协议",,"Fibre Channel (简称FC/FC协议/FC网络/FC互联)：高速高效、配置简单的网络，光纤和铜缆为其传输介质。FC物理层传输带宽很高，从1Gb/s、2Gb/s、 4Gb/s到8Gb/s，采用NMb编码，同步串行传输。FC协议物理层到传输层的逻辑大部分运行在FC适配卡的芯片中，因此FC协议的速度和效率都比 TCP/IP协议高。","2"
"198","FCCT",,,,"2"
"199","FCDNS",,,,"2"
"200","FCELS",,,,"2"
"201","FCFCS",,,,"2"
"202","FCFZS",,,,"2"
"203","FCGI","FCGI协议",,,"2"
"204","FCOE","以太光纤通道",,"以太网光纤通道（FCoE）是一种计算机网络技术，通过以太网网络封装光纤通道帧。 它允许光纤通道在保留光纤通道协议的同时使用万兆以太网（或更高速度）。 该规范是2009年出版的国际信息技术标准委员会T11 FC-BB-5标准的一部分","2"
"205","FCP","光纤通道协议",,"网状信道标准（FCS）定义了一种用于连接工作站、大型机、巨型机、存储设备以及显示设备等的高速数据传输机制。FCS 满足了大量信息的快速传输需求，并减轻了支持当前多通道和网络环境的系统提供商的负担，这是因为 FCS 为网络、存储以及数据传输提供了一个单一标准。网状信道协议是一种在网状信道上的 SCSI 接口协议","2"
"206","FCSBCCS","FC单字节指令码集",,,"2"
"207","FCSWILS","FC-SW内部链路服务",,,"2"
"208","FDDI","FDDI协议",,"光纤分布式数据接口（Fiber Distributed Data Interface，缩写FDDI）是美国国家标准学会制定的在光缆网络上发送数字和音频信号的一组协议。虽然 FDDI 逻辑上是基于 token ring-based 的基点架构，但是却不是以 IEEE 802.5 协定为基础所定义的，取而代之的是衍生自 IEEE 802.4 token bus 协定条仪。","2"
"209","FDP","铸造发现协议",,"铸造发现协议(铸造网络)","2"
"210","FEFD",,,,"2"
"211","FF","现场总线网络",,"现场总线网络（FF）指的是在wireshark的一种协议。现场总线网络（FF）的结构及功能：a.物理层：规定了信号如何发送b.数据链路层：规定如何在设备间共享网络和调度通信c.应用层<FAS总线访问子层、FAS总线报文规范子层>：规定了在设备间交换数据、命令、事件信息以及请求应答中的信息格式与服务d.用户层：用于组成用户所需要的应用程序","2"
"212","FINGER","finger服务",,"显示有关运行 Finger 服务或 Daemon 的指定远程计算机（通常是运行 UNIX 的计算机）上用户的信息。该远程计算机指定显示用户信息的格式和输出。如果不使用参数，Finger 将显示帮助。","2"
"213","FIP","FIP协议",,,"2"
"214","FIX","金融信息交换协议",,"FIX协议是由国际FIX协会组织提供的一个开放式协议，目的是推动国际贸易电子化的进程，在各类参与者之间，包括投资经理、经纪人，买方、卖方建立起实时的电子化通讯协议。FIX协议的目标是把各类证券金融业务需求流程格式化，使之成为一个个可用计算机语言描述的功能流程，并在每个业务功能接口上统一交换格式，方便各个功能模块的连接。","2"
"215","FLEXNET","Flexnet",,"Flexera Software 公司旗下的一系列软件加密、软件授权和企业软件资产优化管理解决方案的统称，包含FlexNet Producer Suite和FlexNet Manager Suite两个套件，其中FlexNet Producer Suite软件授权管理方案能有效帮助软/硬件生厂商简化、高效的管理和控制软件授权的分发，定价，以便企业能以更快的速度和更大的灵活性更好的服务于他们的用户，FlexNet授权管理方案还有效的保护了企业分发的软件不被授权以外的市场滥用。","2"
"216","FLIP",,,,"2"
"217","FORCES","Forwarding and Control Element Separation",,"ForCES的目标是打破网络件的封闭性。分离转发和控制件，自然就要定义他们的开放的通信接口。使用ForCES网络设备的控制件（CE）和转发件（FE）可以独立发展，只要它们支持ForCES协议。这样，网络设备的发展将会加快。","2"
"218","Frame_Relay","传输模式FrameRelay",,"是一种以帧为数据单位的传输模式。","2"
"219","FRACTALGENERATOR",,,,"2"
"220","FTP","文件传输协议",,"FTP 是File Transfer Protocol（文件传输协议）的英文简称，而中文简称为“文传协议”。用于Internet上的控制文件的双向传输。同时，它也是一个应用程序（Application）。基于不同的操作系统有不同的FTP应用程序，而所有这些应用程序都遵守同一种协议以传输文件。在FTP的使用当中，用户经常遇到两个概念：""下载""（Download）和""上传""（Upload）。""下载""文件就是从远程主机拷贝文件至自己的计算机上；""上传""文件就是将文件从自己的计算机中拷贝至远程主机上。用Internet语言来说，用户可通过客户机程序向（从）远程主机上传（下载）文件。","2"
"221","G723","G.723",,"G.723是ITU-T在1996年制订成型的一种多媒体语音编解码标准。其典型应用包括VoIP服务、H.324视频电话、无线电话、数字卫星系统、数电倍增设备(DCME)、公共交换电话网（PSTN）、ISDN及各种多媒体语音信息产品。G.723标准传输码率有5.3kb/s和6.3kb/s两种，在编程过程中可随时切换。该标准主要包含了编码算法和解码算法。","2"
"222","GADU","Gadu Gadu",,"Gadu - Gadu是波兰最流行的即时通，注册用户超过1500万，每天大约650万注册用户在线。","2"
"223","GDSDB",,,,"2"
"224","GEARMAN","Gearman协议",,"Gearman协议工作于TCP之上，默认使用4730端口。它之前使用端口7003,但与AFS的端口范围冲突，4730端口是由IANA分配的。client和jobserver间，以及worker与jobserver间存在通信交互，这两种情况下的通信协议都是由请求包和响应包组成。所有发送到jobserver的包都认为是请求，所有由jobserver发送的包都认为是响应","2"
"225","GED125",,,,"2"
"226","GIFT","gift",,"gift域名表示的含义为: 礼物；天赋；赠品；礼品。.gift为顶级域名，同.com同一级别。","2"
"227","GIT","分布式版本控制系统",,"Git是一个开源的分布式版本控制系统，可以有效、高速的处理从很小到非常大的项目版本管理。 Git 是 Linus Torvalds 为了帮助管理 Linux 内核开发而开发的一个开放源码的版本控制软件。","2"
"228","GLBP","网关负载均衡协议",,"glbp全称Gateway Load Balancing Protocol(网关负载均衡协议)，是思科的专有协议。GLBP不仅提供冗余网关，还在各网关之间提供负载均衡。","2"
"229","GMHDR",,,,"2"
"230","GMR1","GMR-1标准",,"GMR-1标准是基于GSM或3G核心网的GEO卫星系统的空中接口规范。","2"
"231","GNUTELLA","Gnutella",,"Gnutella是简单又方便的网络交换文件完全分布式的p2p通信协议，提供另外一种更简单的交换文件方式给大家选择。","2"
"232","GOOSE","面向通用对象的变电站事件",,"面向通用对象的变电站事件。它是IEC61850中的一种快速报文传输机制，用于传输变电站内IED之间重要的实时性信号。GOOSE采用网络信号代替了常规变电站装置之间硬接线的通信方式，大大简化了变电站二次电缆接线。","2"
"233","GOPHER","GOPHER",,"Gopher是Internet上一个非常有名的信息查找系统，它将Internet上的文件组织成某种索引，很方便地将用户从Internet的一处带到另一处。在WWW出现之前，Gopher是Internet上最主要的信息检索工具，Gopher站点也是最主要的站点，使用tcp70端口。但在WWW出现后，Gopher失去了昔日的辉煌。现在它基本过时，人们很少再使用它；","2"
"234","GRE","通用路由封装",,"通用路由封装（GRE: Generic Routing Encapsulation）是通用路由封装协议，可以对某些网络层协议的数据报进行封装，使这些被封装的数据报能够在IPv4网络中传输","2"
"235","GSM","全球移动通信系统",,"全球移动通信系统Global System for Mobile Communication就是众所周知的GSM，是当前应用最为广泛的移动电话标准。全球超过200个国家和地区超过10亿人正在使用GSM电话。GSM标准的无处不在使得在移动电话运营商之间签署""漫游协定""后用户的国际漫游变得很平常。 GSM 较之它以前的标准最大的不同是它的信令和语音信道都是数字式的，因此GSM被看作是第二代 (2G)移动电话系统。 这说明数字通讯从很早就已经构建到系统中。GSM是一个当前由3GPP开发的开放标准。","2"
"236","GSM_TAP","GSM计费协议",,"GSM计费协议","2"
"237","GTP","GTP",,"GTP是一组基于IP的高层协议，位于TCP/IP或UDP/IP等协议上，主要用于在GSM和UMTS和LTE网络中支持通用分组无线服务(GPRS)的通讯协议。","2"
"238","GVCP","Gig Vision Control Protocol",,"一种控制协议","2"
"239","H223","H.223协议",,"H.223是基于分组的复用协议,主要用于低比特率的多媒体通信。","2"
"240","H225","H.225协议",,"H.225 是由 ITU-T 定义的 H.323 VOIP 体系结构中的一种主要协议，是 H.200/AV.120-Series Recommendations 中包含窄带视频电话服务的一种标准。","2"
"241","H248","H.248协议",,"H.248协议是 2000年由ITU-T第16工作组提出的媒体网关控制协议，它是在早期的MGCP协议基础上改进而成。H.248/MeGaCo协议是用于连接MGC（媒体网关控制器）与MG（媒体网关）的网关控制协议，应用于媒体网关与软交换设备之间及软交换与 H.248/MeGaCo终端之间，是软交换应支持的重要协议。","2"
"242","H261","H.261",,"H.261是1990年ITU-T制定的一个视频编码标准，属于视频编解码器。","2"
"243","H263","H.263",,"H.263是由ITU-T制定的视频会议用的低码率视频编码标准，属于视频编解码器。H.263最初设计为基于H.324的系统进行传输（即基于公共交换电话网和其它基于电路交换的网络进行视频会议和视频电话）。后来发现H.263也可以成功的应用与H.323（基于RTP/IP网络的视频会议系统），H.320（基于综合业务数字网的视频会议系统），RTSP（流式媒体传输系统）和SIP（基于因特网的视频会议）。","2"
"244","H264","H.264",,"H.264，同时也是MPEG-4第十部分，是由ITU-T视频编码专家组（VCEG）和ISO/IEC动态图像专家组（MPEG）联合组成的联合视频组（JVT，Joint Video Team）提出的高度压缩数字视频编解码器标准。这个标准通常被称之为H.264/AVC（或者AVC/H.264或者H.264/MPEG-4 AVC或MPEG-4/H.264 AVC）而明确的说明它两方面的开发者。","2"
"245","H450","H.450协议",,"H.450系列协议是H.323实现补充业务的协议。其中H.450.1定义了适用于各种补充业务控制的通用协议。","2"
"246","H501","H.501",,,"2"
"247","HANDLE","handle协议",,"Handle协议是由美国国家研究推进机构(CorporationforNationalResearchInitiatives)支持的一个项目[10]。Handle系统本身是一个全球化的命名系统,类似于DNS。DNS系统主要应用在对网络的主机进行命名,Handle系统对这一概念作了扩展,它是面向数字化对象的统一命名系统。 ","2"
"248","HARTIP","HARTIP协议",,"Hart-IP是一个额外的连接选项，设备主机系统和资产管理应用程序使用工厂现有的网络基础设施，访问和收集来自现场设备的测量和诊断信息。","2"
"249","HAZELCAST","Hazelcast",,"Hazelcast作为一个高度可扩展的数据分发和集群平台，提供了高效的、可扩展的分布式数据存储、数据缓存。Hazelcast是开源的，在分布式技术方面，Hazelcast提供了十分友好的接口供开发者选择，如Map，Queue，ExecutorService, Lock和Jcache。","2"
"250","HCI","人机交互",,"HCI是Human-Computer Interaction的缩写，意思是人机交互，指人与计算机之间传递、交换信息的媒介和对话接口，是计算机系统的重要组成部分。","2"
"251","HDFS","Hadoop Distributed File System",,"Hadoop分布式文件系统(HDFS)被设计成适合运行在通用硬件(commodity hardware)上的分布式文件系统。它和现有的分布式文件系统有很多共同点。但同时，它和其他的分布式文件系统的区别也是很明显的。HDFS是一个高度容错性的系统，适合部署在廉价的机器上。HDFS能提供高吞吐量的数据访问，非常适合大规模数据集上的应用。HDFS放宽了一部分POSIX约束，来实现流式读取文件系统数据的目的。HDFS在最开始是作为Apache Nutch搜索引擎项目的基础架构而开发的。HDFS是Apache Hadoop Core项目的一部分。","2"
"252","HDFSDATA",,,,"2"
"253","HIP","HIP协议",,"HIP协议（Host Identity Protoco（l）是由 Robert Moskowitz 等人提出的针对上述问题的一个解决方案。HIP的实现是在第三层网络层与第四层传输层中间插入 3. 5 层主机标志层，用于标志连接终端。在 IPv6 中，HIP 负载实际上类似于 IPv6 的一个扩展头。","2"
"254","HNBAP","HNBAP协议",,,"2"
"255","HOMEPLUG","家庭插电联盟",,"Home Plug全称Home Plug PowerLine Alliance，译为家庭插电联盟(全称为家庭插座电力线联盟)，是一个非盈利的组织。该联盟由松下、英特尔、惠普、夏普等13家公司于2000年3月成立，现已发展成为由90家公司组成的企业联盟。","2"
"256","HP",,,,"2"
"257","HPEXT","HPEXT协议",,,"2"
"258","HPFEEDS","hpfeeds协议",,"hpfeeds是一个轻量级的验证发布-订阅协议(authenticated publish-subscribe protocol)。","2"
"259","HPSW","HPSW协议",,,"2"
"260","HPTEAM",,,,"2"
"262","HSRP","热备份路由器协议",,"热备份路由器协议（HSRP）的设计目标是支持特定情况下 IP 流量失败转移不会引起混乱、并允许主机使用单路由器，以及即使在实际第一跳路由器使用失败的情形下仍能维护路由器间的连通性。实现HSRP的条件是系统中有多台路由器，它们组成一个“热备份组”，这个组形成一个虚拟路由器。换句话说，当源主机不能动态知道第一跳路由器的 IP 地址时，HSRP 协议能够保护第一跳路由器不出故障","2"
"263","HTTP","超文本传输协议",,"超文本传输协议（HTTP，HyperText Transfer Protocol)是互联网上应用最为广泛的一种网络协议。所有的WWW文件都必须遵守这个标准。设计HTTP最初的目的是为了提供一种发布和接收HTML页面的方法。1960年美国人Ted Nelson构思了一种通过计算机处理文本信息的方法，并称之为超文本（hypertext）,这成为了HTTP超文本传输协议标准架构的发展根基。Ted Nelson组织协调万维网协会（World Wide Web Consortium）和互联网工程工作小组（Internet Engineering Task Force ）共同合作研究，最终发布了一系列的RFC，其中著名的RFC 2616定义了HTTP 1.1","2"
"264","HYPERSCSI","HyperSCSI",,,"2"
"265","I2C","总线协议",,"I2C总线是由Philips公司开发的一种简单、双向二线制同步串行总线。它只需要两根线即可在连接于总线上的器件之间传送信息。","2"
"266","IAPP","IAPP协议",,"接入点互操作协议","2"
"267","IAX2","IAX2协议",,,"2"
"268","ICAP","ICAP协议",,"ICAP是Internet Content Adaptation Protocol的缩写.它在本质上是在HTTP message上执行RPC远程过程调用的一种轻量级的协议, 也就是说, 它让ICAP Client可以把HTTP Message传给ICAP Server,  然后ICAP Server可以对其进行某种变换或者其他处理(“匹配”).被变换的message可以是HTTP请求也可以是HTTP应答","2"
"269","ICEP","ICEP协议",,,"2"
"270","ABIS","Abis接口协议",,"在Abis接口，涉及的协议不多，主要有链路层的LapD协议和第三层协议（规范并没有专门为这一层协议其起名字，因此后面我们都称其为Abis层3协议）。","2"
"271","ACTRACE","ACtrace协议",,,"2"
"272","ICP","ICP协议",,"ICP是一个在网络中高速缓存服务器间进行通信的轻量级的消息格式。ICP用于缓存服务器之间交换关于URL地址是否存在的消息。缓存间通过交换ICP的产需和会发消息来收集信息，以便选择需要检索的对象的最合理的位置。
","2"
"273","ICQ","ICQ",,"ICQ是一款即时通讯软件。1996年，三个以色列人维斯格、瓦迪和高德芬格聚在一起，决定开发一种使人与人在互联网上能够快速直接交流的软件。他们为新软件取名ICQ，即“I SEEK YOU（我找你）”的意思。","2"
"274","IDM","综合数据复用器",,"IDM (Integrated Data Multiplexer)综合数据复用器是一种数据复用设备，它可以将多路RS232、RS485及数字语音等多种数据复用到E1传输通道或光传输通道内，实现不同类型数据在同一通道内的复用、传输。","2"
"275","IDP","数据报协议",,,"2"
"276","IEC104","IEC60870-5-104规约",,"IEC104规约是一个广泛应用于电力、城市轨道交通等行业的国际标准。","2"
"277","IEEE80211","无线局域网标准",,"802.11协议簇是国际电工电子工程学会（IEEE）为无线局域网络制定的标准。虽然WI-FI使用了802.11的媒体访问数据链路层（DLL）和物理层（PHY），但是两者并不完全一致。在以下标准中，使用最多的应该是802.11n标准，工作在2.4GHz或5GHz频段，可达600Mbps（理论值）。","2"
"278","IEEE802154","IEEE802154协议",,"IEEE802.15.4-2003协议规范规定了一个MAC层和两个PHY层。","2"
"279","IEEE8021AH","IEEE802.1AH标准",,,"2"
"280","IEEE802A","IEEE802A",,"IEEE 802又称为LMSC（LAN /MAN Standards Committee， 局域网/城域网标准委员会），致力于研究局域网和城域网的物理层和MAC层中定义的服务和协议，对应OSI网络参考模型的最低两层（即物理层和数据链路层）","2"
"281","IGMP","网际组管理协议",,"Internet 组管理协议称为IGMP协议（Internet Group Management Protocol），是因特网协议家族中的一个组播协议。该协议运行在主机和组播路由器之间。IGMP协议共有三个版本，即IGMPv1、v2 和v3。","2"
"282","IGRP","内部网关路由协议",,"IGRP (Interior Gateway Routing Protocol)是一种内部网关路由协议，它由Cisco公司八十年代中期设计。使用组合用户配置尺度，包括延迟、带宽、可靠性和负载。缺省情况下，IGRP每90秒发送一次路由更新广播，在3个更新周期内（即270秒），没有从路由中的第一个路由器接收到更新，则宣布路由不可访问。在7个更新周期即630秒后，Cisco IOS 软件从路由表中清除路由。","2"
"283","ILP","ILP协议",,"信息泄露防护","2"
"284","MAIL_IMAP","交互式邮件访问协议",,"IMAP是一种邮件访问协议，但是它比POP3有更多的特色，但同时也比POP3要更复杂。它是双向的，将本地的邮件与远程的服务器联系起来。在本地所进行的操作可以与邮件服务器同步","2"
"285","INFINIBAND","无限带宽技术",,"InfiniBand架构是一种支持多并发链接的“转换线缆”技术，在这种技术中，每种链接都可以达到2.5 Gbps的运行速度。这种架构在一个链接的时候速度是500 MB/秒，四个链接的时候速度是2 GB/秒，12个链接的时候速度可以达到6 GB /秒。","2"
"286","3COM","华为3COM认证",,"华为3Com认证完全继承了华为认证体系和资源平台，并将充分考虑客户不同层次的需求，增加专项网络技术认证，致力于为全球客户提供全面、专业、权威的网络技术认证。 华为3Com认证是中国第一家建立国际规范的完整的网络技术认证体系，也是中国第一个走向国际市场的IT厂商认证，在产品和教材上都具有完全的自主知识产权，具有很高的技术含量，并专注于客户技术和技能的提升，得到了电信运营商、国防系统、行业客户和高校学生的广泛认可，成为业界有影响的认证品牌之一。","2"
"287","IPCP","IPCP",,"IPCP(IP Control Protocol)，即为IP控制协议，用于建立，配置和检测数据链路连接的连接控制协议（LCP）以及用于建立和配置不同网络层协议的网络控制协议（NCP）协议族。","2"
"288","IPDC","IP设备控制",,,"2"
"289","IPFC","线间潮流控制器",,"线间潮流控制器(IPFC)是柔性交流输电系统(FACTS)控制装置中的一种新装置，它可以控制多条传输线的潮流，为了提高系统运行过程中的可靠性，采用功率注入法证实线间潮流控制器(IPFC)有可以同时调节母线电压，有功功率，无功功率以及降低功率损耗的能力。","2"
"290","IPHC","互联网协议头压缩",,,"2"
"291","IPMI","智能平台管理接口 ",,"IPMI是一个智能平台管理接口。用户可以利用IPMI 监视服务器等设备的物理特征，如各部件的温度、电压、风扇工作状态、电源供应以及机箱入侵等。","2"
"292","IPNET",,,,"2"
"293","IPOIB","IPoIB协议",,"IPoIB（Internet Protocol over InfiniBand），顾名思义，就是利用物理IB网络（包括服务器上的IB卡，IB连接线，IB交换机等）通过IP协议进行连接，并进行数据传输。","2"
"294","IPSEC_ESP","IPsec 封装安全负载",,"IPsec 封装安全负载（IPsec ESP）是 IPsec 体系结构中的一种主要协议，其主要设计来在 IPv4 和 IPv6 中提供安全服务的混合应用。IPsec ESP 通过加密需要保护的数据以及在 IPsec ESP 的数据部分放置这些加密的数据来提供机密性和完整性。且ESP加密采用的是对称密钥加密算法，能够提供无连接的数据完整性验证、数据来源验证和抗重放攻击服务。根据用户安全要求，这个机制既可以用于加密一个传输层的段（如：TCP、UDP、ICMP、IGMP），也可以用于加密一整个的 IP 数据报。封装受保护数据是非常必要的，这样就可以为整个原始数据报提供机密性。","2"
"295","IPSICTL",,,,"2"
"296","ACAP","应用配置访问协议",,,"2"
"297","IPV6CP","P 控制协议和 IPv6 控制协议",,"IP 控制协议（IPCP）和 IPv6 控制协议（IPv6CP）是一种网络控制协议，用于建立和配置 PPP 上的 IP 或 IPv6，它提供一种方法，通过 PPP 协商和使用 Van Jacobson TCP/IP 头部压缩。","2"
"298","IPVS","IP虚拟服务器",,"ipvs称之为IP虚拟服务器（IP Virtual Server，简写为IPVS）。是运行在LVS下的提供负载平衡功能的一种技术","2"
"299","IPX","IPX",,"IPX 是互联网分组交换协议，提供分组寻址和选择路由的功能,保证可靠到达，相当于数据报的功能;SPX 是顺序报文分组交换协议，它可保证信息流按序、可靠地传送; IPX/SPX 为Nove 11网在网络层和传输层采用的协议;SDLC 是SNA 中的数据链路层协议,后修改为HDLC(高级数据链路控制);NFS是SUN 制定的网络文件服务标准;ODBC 是微软制定的异构数据库互访的标准,真正体现了数据库开放性。","2"
"300","IPXWAN","广域网中的IPX",,"IPX 是互联网分组交换协议，提供分组寻址和选择路由的功能,保证可靠到达，相当于数据报的功能;SPX 是顺序报文分组交换协议，它可保证信息流按序、可靠地传送; IPX/SPX 为Nove 11网在网络层和传输层采用的协议;SDLC 是SNA 中的数据链路层协议,后修改为HDLC(高级数据链路控制);NFS是SUN 制定的网络文件服务标准;ODBC 是微软制定的异构数据库互访的标准,真正体现了数据库开放性","2"
"301","IRC","互联网中继聊天",,"IRC是Internet Relay Chat 的英文缩写，中文一般称为互联网中继聊天。它是由芬兰人Jarkko Oikarinen于1988年首创的一种网络聊天协议。经过十年的发展，目前世界上有超过60个国家提供了IRC的服务。IRC的工作原理非常简单，您只要在自己的PC上运行客户端软件，然后通过因特网以IRC协议连接到一台IRC服务器上即可。它的特点是速度非常之快，聊天时几乎没有延迟的现象，并且只占用很小的带宽资源。所有用户可以在一个被称为\""Channel\""（频道）的地方就某一话题进行交谈或密谈。每个IRC的使用者都有一个Nickname（昵称）。","2"
"302","IPSec_ISAKMP","Internet 安全连接和密钥管理协议",,"Internet 安全连接和密钥管理协议（ISAKMP）是 IPsec 体系结构中的一种主要协议。该协议结合认证、密钥管理和安全连接等概念来建立政府、商家和因特网上的私有通信所需要的安全。","2"
"303","ISDN","ISDN接口协议",,"ISDN用户―网络接口有两种接口结构，即BRI和PRI。","2"
"304","ISIS","中间系统到中间系统",,"ISIS是一个分级的链接状态路由协议，基于DECnet PhaseV 路由算法，实际上与OSPF非常相似，它也使用Hello协议寻找毗邻节点，使用一个传播协议发送链接信息。ISIS可以在不同的子网上操作，包括广播型的LAN、WAN和点到点链路。","2"
"305","ISMACRYP","ISMACRYP协议",,"Ismacryp 协议全称为ISMA Encryption and Authentication,目前版本为2.0，与前一个版本1.1比较，文档扩充到了能存储在ISO base format（ISO/IEC 14496-12,这种格式与传统的TS或ASF会很不一样，在流传输方面有很大的灵活性，在其他文档上做一个介绍）上的任意的编码。从1.0，1.1，2.0文档来看，文档的升级版本会对前一个版本进行扩充和更新，相同的内容其细节的完整部分则在前一个版本的文档中阐述。1.0文档是对mpeg-4 part2及mpeg-4 aac编码的加密和结构做了一个规范，1.1文档扩充到h264/avc 和he-aac编码，2.0文档扩充到任意编码（可存储在ISO base format），需要说明的是，1.1和1.0版本的结构不是兼容的，2.0相比于1.1则添加了新的模式来扩充新的功能。","2"
"306","ISMP","ISMP协议",,,"2"
"307","ISNS","isns",,"iSNS （Internet 存储名称服务协议）通过提供的一组类似于光纤信道网络上可使用的服务，推动了 IP 网络中 iSCSI 和光纤信道存储设备可扩展配置和管理。","2"
"308","ISUP","用户部分",,"ISUP，即ISDN用户部分，是 SS7/C7 信令系统的一种主要协议，定义了协议和程序用于建立、管理和释放中继电路，该中继电路在公共交换电话网络（PSTN）上传输语音和数据呼叫。","2"
"309","ITDM",,,,"2"
"310","IUA","IUA",,"IUA ，全称ISDN User Adaptation Layer，是指ISDN用户适配层协议，它的主要功能是适配传送ISDN的用户信息给IP数据库，提供ISDN的网管互通功能。","2"
"311","IUUP","IUUP协议",,"IUUP是Iu接口的无线网络层用户面协议，通常用来传输与RAB相关的用户数据。一个IUUP的实例与并且仅能够与一个RAB相关，对于终端用户来说，如果同时简历了多个RAB,则需要同时有相同数量的多个IUUP实例。
","2"
"312","IXVERIWAVE","IxVeriwave WIFI测试仪",,"IxVeriWave是业界广泛使用的WIFI测试仪表，能够支持到IEEE802.11a/b/g/n/ac接口以及10/100/1000Mbps以太网接口。既可以支持AP的仿真，也可以支持client的仿真。而且可精确地创建具有可重复性的业务流量，可为设备的功能、性能提供独特的测试方法。","2"
"313","JFIF","jfif",,"图片存储格式之一，由JPEG格式衍生而来，后缀为"".jfif""。","2"
"314","JMIRROR",,,,"2"
"315","JPEG","联合图像专家小组",,"JPEG 是Joint Photographic Experts Group（联合图像专家小组）的缩写，是第一个国际图像压缩标准。JPEG图像压缩算法能够在提供良好的压缩性能的同时，具有比较好的重建质量，被广泛应用于图像、视频处理领域。人们日常碰到的“.jpeg”、‘’.jpg“等指代的是图像数据经压缩编码后在媒体上的封存形式，不能与JPEG压缩标准混为一谈。","2"
"316","JUNIPER","Juniper协议",,,"2"
"317","K12","K12即时通",,"K12即时通通过桌面集成办公系统的信息终端与其它应用软件的整合，信息由“拉”（用户打开网页浏览）变为“推”（应用软件主动把信息发到桌面），解决了网络应用中信息反馈不及时、繁琐、不实用的弊端，使数字化应用变得更加简单、即时、快捷、容易操作，为用户节省时间和精力，创造出网络办公的新局面，使网络应用成为一种乐趣和享受，通过这种方式可以将各应用软件模块中的产生的各自不关联的、孤立的系统信息有效地集中到桌面集成办公系统的终端，解决“信息孤岛”问题。","2"
"318","KAFKA","Apache Kafka",,"Kafka是由Apache软件基金会开发的一个开源流处理平台，由Scala和Java编写。Kafka是一种高吞吐量的分布式发布订阅消息系统，它可以处理消费者规模的网站中的所有动作流数据。 这种动作（网页浏览，搜索和其他用户的行动）是在现代网络上的许多社会功能的一个关键因素。 这些数据通常是由于吞吐量的要求而通过处理日志和日志聚合来解决。 对于像Hadoop的一样的日志数据和离线分析系统，但又要求实时处理的限制，这是一个可行的解决方案。Kafka的目的是通过Hadoop的并行加载机制来统一线上和离线的消息处理，也是为了通过集群来提供实时的消费。","2"
"319","KDP","密钥分发协议",,,"2"
"320","KDSP",,,,"2"
"321","KERBEROS","Kerberos协议",,"是一种网络认证协议，其设计的目的是通过秘钥系统为客户机/服务器应用程序提供强大的认证服务。","2"
"322","KINGFISHER","KINGFISHER协议",,,"2"
"323","KINK","KINK协议",,,"2"
"324","KISMET","kismet",,"Kismet 是一款工作在 802.11 协议第二层的无线网络检测、嗅探、干扰工具。可以工作在支持raw监控模式的所有无线网卡上。可以嗅探包括 802.11b, 802.11a, 和 802.11g 在内的协议包。","2"
"325","KNET",,,,"2"
"326","KPASSWD","KPASSWD协议",,,"2"
"327","KRB4","KRB4协议",,,"2"
"328","KT","KT函数库",,"
KT它是一个免费的、开源的（采用LGPL开源协议）函数库。它是Kingthy的个人开发库，它也可以算是一个小的开发框架包。","2"
"329","L1","L1协议栈",,"L1层或称物理层，提供物理介质上的比特流传输，遵循GSM技术05系列规范，为上层软件提供服务，且控制逻辑信道到物理信道的映射和安排、无线控制以及TDMA帧。","2"
"330","L2TP","L2TP",,"L2TP是一种工业标准的Internet隧道协议，功能大致和PPTP协议类似，比如同样可以对网络数据流进行加密。不过也有不同之处，比如PPTP要求网络为IP网络，L2TP要求面向数据包的点对点连接；PPTP使用单一隧道，L2TP使用多隧道；L2TP提供包头压缩、隧道验证，而PPTP不支持。","2"
"331","LAPB","链路访问过程平衡",,"LAP-B 是源于 HDLC 的一种面向位的协议，它实际上是 ABM （平衡的异步方式类别）方式下的 HDLC。LAP-B 能够确保传输帧的无差错和正确排序。","2"
"332","LAPBETHER","LAPBETHER协议",,,"2"
"333","LAPD","LAPD协议",,"LAPD用于BTS与BSC之间的Abis接口上的链路层。LAPD消息一般由一些固定的帧组成,而且这些帧都会形成它自己的帧结构以便在消息传递双方传递数据。LAPD上的帧结构有三种:信息帧、监视帧、未编号帧。下面我们来看看在GSM中LAPD用到的一些帧类型","2"
"334","LAPLINK","LAPLINK协议",,,"2"
"335","LCP","链路控制协议",,"链路控制协议，简称LCP（Link Control Protocol）。它是PPP协议的一个子集，在PPP通信中，发送端和接收端通过发送LCP包来确定那些在数据传输中的必要信息。","2"
"336","LCSAP",,,,"2"
"337","LDAP","轻量目录访问协议",,"LDAP是轻量目录访问协议，英文全称是Lightweight Directory Access Protocol，一般都简称为LDAP。它是基于X.500标准的，但是简单多了并且可以根据需要定制。与X.500不同，LDAP支持TCP/IP，这对访问Internet是必须的。LDAP的核心规范在RFC中都有定义，所有与LDAP相关的RFC都可以在LDAPman RFC网页中找到","2"
"338","LDP","标签分发协议",,"标签分发协议LDP（Label Distribution Protocol）是 MPLS 体系中的一种主要协议。在 MPLS 网络中，两个标签交换路由器（LSR）必须用在它们之间或通过它们转发流量的标签上达成一致。","2"
"339","LDSS",,,,"2"
"340","LGE","LGE协议",,,"2"
"341","LINX",,,,"2"
"342","LISP","定位编号分离协议",,"LISP是定位编号分离协议的英文Locator ID Separation Protocol的缩写。实质是一个IPinIP的协议。","2"
"343","LLC","逻辑链路控制",,"LLC是Logical Link Control的缩写，意思为逻辑链路控制子层。LLC负责识别网络层协议，然后对它们进行封装。LLC报头告诉数据链路层一旦帧被接收到时，应当对数据包做何处理。","2"
"344","LLCGPRS",,,,"2"
"345","LLDP","链路层发现协议",,"链路层发现协议（LLDP）是一个厂商无关的二层协议，它允许网络设备在本地子网中通告自己的设备标识和性能。","2"
"346","LLRP","底层读写器协议",,"底层读写器协议(LLRP)作为射频识别系统的标准底层协议,为射频识别系统的广泛应用提供了先决条件;通过开发LLRP协议工具包,阐述LLRP协议的工作原理以及工具包的设计、实现和功能性测试;该工具包为射频识别中对读写器进行管理配置以及对标签进行盘存、访问操作提供标准接口;通过使用该工具包,开发人员可以快速且有效地完成射频识别应用系统软件开发","2"
"347","LLT","低延迟传输协议",,,"2"
"348","LMI","帧中继本地管理接口",,"本地管理接口，是在DTE设备和FR之间的一种信令标准，它负责管理链路连接和保持设备间的状态。","2"
"349","LMP","LMP",,"LMP（(Link Management Protocol)）链路管理协议。","2"
"350","LON","LON",,"LON，即LonWorks协议，能够为设计、创建、安装和维护设备网络方面的许多问题提供解决方案。","2"
"351","LOOP","LOOP协议",,"LOOP协议是一个指令序列将被连续的重复执行直到满足一定条件。典型的，一个特定的过程，比如说得到一个数据对象并改变它，执行之后检一个计数器是否达到一个预定的数。如果还没有，则队列里的下一条指令是回到的队列中的第一条指令并重复这个队列。","2"
"352","LPD","LPD",,"行式打印机后台程序是一个安装在UNIX打印服务器上的后台程序。它的功能是等待接受客户使用行式打印机远程（LPR）协议传来的打印工作。当LPD收到一个打印任务后，它先将打印任务暂存于打印队列中，打印队列是一个文件子目录，其中有许多打印工作等待LPD进行处理。当打印设备空闲时，LPD从打印队列中取出打印任务并将它传给打印机进行打印。","2"
"353","LPPA","定位协议A",,"A（LPPa）主要描述LTE定位协议A，包括：定位辅助信息的获取和传输，定位相关测量信息和位置信息的交互等终端一致性系列规范","2"
"354","LSC","LSC",,"小灵通定位系统（LSC：Location Service Center）则通过小灵通基站间的精确的三点定位，能够将定位精度控制在50米范围内，居目前国内同类业务的领先地位。它将为不同的行业和个人用户提供功能强大的定位信息服务，如紧急位置服务、跟踪和物流管理业务、位置信息服务等。","2"
"355","LTP","LTP协议",,"应用于太空通信","2"
"356","LWAPP","轻型接入点协议(lwapp)",,"现在， WLAN 领域出现了向集中智能和控制发展的趋势。在此新架构中，使用一个 WLAN 控制器系统来为大量轻型接入点创建和执行策略。通过集中这些设备的智能特性，整个无线企业中对 WLAN 运营至关重要的安全性、移动性、服务质量 (QoS) 和其他功能都可得到有效管理。此外，通过分离接入点和控制器的功能， IT 人员能简化管理、提高性能并使大型无线网络更为安全。","2"
"357","LWRES","LWRES协议",,,"2"
"358","M2PA","M2PA",,"M2PA：MTP2 Peer-to-peer user Adaptation MTP第二级用户的对等适配层协议，SIGTRAN协议组中的一个协议，该协议允许信令网关向IP SP处理传送MTP3的消息，并提供MTP信令网网管功能。M2PA是基于端对端而被使用的，并且代替了传统的SS7链路所提供的功能。另外，它还是针对开放SS7网络设备的。","2"
"359","M2AP","M2应用协议",,,"2"
"360","M2UA","M2UA",,"M2UA：MTP第二级用户的适配层协议，该协议允许信令网关向对等的IP SP传送MTP3消息，对SS7信令网和IP网提供无缝的网管互通功能。","2"
"361","M3AP","M3应用协议",,,"2"
"362","M3UA","M3UA",,"M3UA（MTP3 User Adaptation）表示MTP（Message Transfer Part，消息传递部分）第三级用户的适配层协议","2"
"363","MAAP","MAAP协议 ",,,"2"
"364","MACCTRL","MACCTRL协议",,,"2"
"365","MACSEC","MACSEC协议",,,"2"
"366","MACTELNET","MACTELNET协议",,,"2"
"367","MANOLITO","MANOLITO协议","P2P","Piolet使用TCP和UDP通讯，默认登陆端口为UDP 41170","2"
"368","MBRTU","MBRTU协议",,,"2"
"369","MBTCP","MBTCP协议",,,"2"
"370","MDSHDR","MDSHDR协议",,,"2"
"371","MEGACO","MEGACO",,"Megaco是一个运行于媒体网关和媒体网关控制器之间的协议,它可以使媒体网关控制器对媒体网关进行控制。它提供控制媒体的建立、修改和释放机制，同时也可携带某些随路呼叫信令，支持传统网络终端的呼叫，解决了H.323的复杂、伸缩性差的问题，是下一代网络关键的媒体网关控制协议。 Megaco是作为集中移动的一部分开发的,这种移动可以在分组交换因特网上将语音和数据集中在一起。Megaco是“媒体网关控制器”的缩写形式。它是由IETF和ITU定义的(这两个组织将该协议指定为H.248)。","2"
"372","MEMCACHE","MemCache",,"memcache是一套分布式的高速缓存系统，由LiveJournal的Brad Fitzpatrick开发，但目前被许多网站使用以提升网站的访问速度，尤其对于一些大型的、需要频繁访问数据库的网站访问速度提升效果十分显著[1]  。这是一套开放源代码软件，以BSD license授权发布。","2"
"373","META","标签协议",,,"2"
"374","VOIP_MGCP","VOIP_MGCP协议",,"VOIP中的媒体网关控制协议","2"
"375","MIH","MIH协议",,,"2"
"376","MIKEY","MIKEY协议",,,"2"
"377","MIME","多用途互联网邮件扩展",,"MIME(Multipurpose Internet Mail Extensions)多用途互联网邮件扩展类型。是设定某种扩展名的文件用一种应用程序来打开的方式类型，当该扩展名文件被访问的时候，浏览器会自动使用指定应用程序来打开。多用于指定一些客户端自定义的文件名，以及一些媒体文件打开方式。","2"
"378","MINT","MINT协议",,,"2"
"379","MIP","移动IP",,"移动IP（Mobile IP，MIP）是Internet工程任务小组（Internet Engineering Task Force，IETF）制订的标准通信协议，允许移动节点（不限于手机）在不改变IP地址的情况下可以从一个子网移动到其他子网。MIP是IP协议的增强，增加了当移动设备连通时把Internet转接进移动设备的机制。","2"
"380","MIP6","移动IPv6工作组",,,"2"
"381","MNDP",,,,"2"
"382","MOJITO",,,,"2"
"383","MOLDUDP",,,,"2"
"384","MOLDUDP64",,,,"2"
"385","MONGO","MONGO协议",,,"2"
"386","PPP_MultiLink","多链路协议",,"多链路协议——MP（Multilink Protocol）是PPP协议的一个扩展协议。","2"
"387","MP2T",,,,"2"
"388","MP4VES","MP4视频编码标准",,,"2"
"389","MPEG","动态图像专家组",,"MPEG（Moving Picture Experts Group，动态图像专家组）是ISO（International Standardization Organization，国际标准化组织）与IEC（International Electrotechnical Commission，国际电工委员会）于1988年成立的专门针对运动图像和语音压缩制定国际标准的组织。","2"
"390","MPEG1","MPEG-1",,"MPEG-1是MPEG组织制定的第一个视频和音频有损压缩标准。视频压缩算法于1990年定义完成。1992年底，MPEG-1正式被批准成为国际标准。MPEG-1是为CD光碟介质定制的的视频和音频压缩格式。一张70分钟的CD光碟传输速率大约在1.4Mbps。而MPEG-1采用了块方式的运动补偿、离散余弦变换（DCT）、量化等技术，并为1.2Mbps传输速率进行了优化。MPEG-1随后被Video CD采用作为核心技术。MPEG-1的输出质量大约和传统录像机VCR，信号质量相当，这也许是Video CD在发达国家未获成功的原因。","2"
"392","MPLSCP","MPLSCP协议",,,"2"
"393","MQ","MQ",,"MQ传递主干，在世界屡获殊荣。 它帮您搭建企业服务总线(ESB)的基础传输层。IBM WebSphere MQ为SOA提供可靠的消息传递。它为经过验证的消息传递主干， 全方位、 多用途的数据传输， 并帮助您搭建企业服务总线的传输基础设施。","2"
"394","MRCPV2","MRCPv2协议",,"媒体资源控制协议(Media Resource Control Protocol，MRCP)是由Cisco、Nuance等公司联合开发的网络协议，该协议由IETF作为Internet草案发布(draft-shanmugham-mrcp-07)。该协议为那些需要进行语音处理的客户端提供了一种通过网络来控制媒体处理资源(如ASR、TTS引擎等)的机制。该协议在设计之初就考虑了可以在将来得到扩展以支持声纹鉴别和身份识别(Speaker Identification/Speaker Verification)等功能。 ","2"
"395","MRP","多重冗余协议",,"MRP：Multi-layersRedundantProtocol）基于联想拥有的大型计算机高可靠设计专利技术，利用电信骨干网可靠性运营维护专业经验，通过在物理层、链路层、网络层、实体层等多个层面实现多元化冗余设计，可有效地保障联想网御防火墙在用户网络应用中的高可用性。","2"
"396","MSDP","组播源发现协议",,"组播源发现协议（MSDP）描述了一种连接多 PIM-SM（PIM-SM ： PIM Sparse Mode） 域的机制。每种 PIM-SM 域都使用自己独立的 RP ，它并不依赖于其它域内的 RP 。","2"
"397","MSMMS","MSMMS协议",,,"2"
"398","MSNLB","MSNLB协议",,,"2"
"399","MSNMS","MSNMS协议",,,"2"
"400","MSPROXY","MS代理协议",,,"2"
"401","MSTP","多生成树协议",,"MSTP（Multi-Service Transfer Platform）（基于SDH 的多业务传送平台）是指基于SDH 平台同时实现TDM、ATM、以太网等业务的接入、处理和传送，提供统一网管的多业务节点。","2"
"402","MTP2","MTP2协议",,"MTP2生成和处理LSSU和FISU，并对MSU进行可靠传输保证","2"
"403","MTP3","MTP3协议",,"规定了信令点之间进行消息传递和与此传递有关的功能和过程","2"
"404","MTP3MG","MTP3MG协议",,,"2"
"405","MUX27010",,,,"2"
"406","MYSQL","MySQL",,"MySQL是一个关系型数据库管理系统，由瑞典MySQL AB 公司开发，目前属于 Oracle 旗下产品。MySQL 是最流行的关系型数据库管理系统之一，在 WEB 应用方面，MySQL是最好的 RDBMS (Relational Database Management System，关系数据库管理系统) 应用软件。","2"
"407","NASDAQ_SOUP",,,,"2"
"408","NAT","网络地址转换",,"NAT（Network Address Translation，网络地址转换）是1994年提出的。当在专用网内部的一些主机本来已经分配到了本地IP地址（即仅在本专用网内使用的专用地址），但现在又想和因特网上的主机通信（并不需要加密）时，可使用NAT方法。","2"
"409","NBIPX","Windows NT跟踪分析",,"NetBOIS没有地址类型，这些地址类型用于数据报在网络上进行路由选择时，然而NetBOIS接口也适用于其它用于数据报前向转发的协议，例如IPX以及TCP/IP。当与IPX网络协议混合使用时，NetBOIS指的是NBIPX","2"
"410","NBT","NBT",,"net bios over TCP/IP属于SMB(Server Message Block) Windows协议族，用于文件和打印共享服务。NBT(NetBIOS over TCP/IP) 使用137(UDP), 138(UDP) and 139 (TCP）来实现基于TCP/IP的NETBIOS网际互联。","2"
"411","NCP","网络控制协议",,"NCP指Network Control Protocol，即网络控制协议，它管理对NetWare 服务器资源的访问。NCP 向NetWare 文件共享协议发送过程调用消息，处理 NetWare 文件和打印资源请求。 NCP 用于 NetWare服务器和客户机之间传输信息的主要协议。同名的NCP还有网络咨询人员和非中心性参数的意思。","2"
"412","NCS","NCS",,"基于电缆网络的呼叫控制信令","2"
"413","NDMP","网络数据管理协议",,"网络数据管理协议（NDMP）是一种基于企业级数据管理的开放协议。NDMP 中定义了一种基于网络的协议和机制，用于控制备份，恢复，以及在主要和次要存储器之间的数据传输。","2"
"414","NDP","邻居发现协议",,"NDP用来发现直接相连的邻居信息，包括邻接设备的设备名称、软/硬件版本、连接端口等，另外还可提供设备的ID、端口地址、硬件平台等信息。","2"
"415","NDPS","NDPS协议",,"一种无线多媒体网络分组调度算法","2"
"416","NETANALYZER","NetAnalyzer",,"NetAnalyzer是一款集网络数据采集、报文协议分析、统计、网络流量监控于一体的网络管理工具软件,用户可以通过该软件采集网络数据,并对相关的数据进行分析,对于网络管理人员或从事网络软件开发的人是一个不错的工具.系统提供多种辅助工具方便用户更加深入的对原始数据进行还原.目前该系统已经支持60多种协议,覆盖TCP/IP协议模型各层,支持EthernetII、PPP、Cisco HDLC、Linux SLL等多种底层网络,并且提供TCP、UDP载荷数据查看,为符合国内用户软件还提供了多种中文编码方式,方便查看中文数据.另外还提供了远程抓包功能,方便对远程机器进行监控.","2"
"417","NETBIOS","NetBIOS协议",,"NetBIOS协议是由IBM公司开发，主要用于数十台计算机的小型局域网。NetBIOS协议是一种在局域网上的程序可以使用的应用程序编程接口（API），为程序提供了请求低级服务的统一的命令集，作用是为了给局域网提供网络以及其他特殊功能，几乎所有的局域网都是在NetBIOS协议的基础上工作的。","2"
"418","NETDUMP",,,,"2"
"419","NETFLOW","NETFLOW",,"NetFlow是一种数据交换方式，其工作原理是：NetFlow利用标准的交换模式处理数据流的第一个IP包数据，生成NetFlow 缓存，随后同样的数据基于缓存信息在同一个数据流中进行传输，不再匹配相关的访问控制等策略，NetFlow缓存同时包含了随后数据流的统计信息。","2"
"420","NETMON","NETMON",,"Netmon是一个Windows平台下的轻量级网络监视器，通过对网络流量进行基于进程的分类，Netmon提供实时流量监控、每日流量统计、流量的协议分布、大小分布、速率分布直方图及每个报文的详细信息等多个视图。","2"
"421","NETROM","NETROM协议",,,"2"
"422","NETSYNC","同步协议",,,"2"
"423","NETTL","NETTL协议",,,"2"
"424","NEWMAIL",,,,"2"
"425","NEW_CREATE_DISSECTOR",,,,"2"
"426","NFLOG",,,,"2"
"427","NFS","网络文件系统",,"NFS（Network File System）即网络文件系统，是FreeBSD支持的文件系统中的一种，它允许网络中的计算机之间通过TCP/IP网络共享资源。在NFS的应用中，本地NFS的客户端应用可以透明地读写位于远端NFS服务器上的文件，就像访问本地文件一样。","2"
"428","NHRP","下一跳解析协议",,"下一跳解析协议（NHRP）用于连接到非广播多路访问（NBMA）式子网络的源站（主机或路由器）决定到达目标站间的 “ NBMA 下一跳 ”的互联网络层地址和 NBMA 子网地址。如果目的地与 NBMA 子网连接， NBMA 下一跳就是目标站；否则， NBMA 下一跳是从 NBMA 子网到目标站最近的出口路由器。 NHRP 被设计用于 NBMA 子网下的多重协议互联网络层环境中。","2"
"429","NLSP","NetWare 链路服务协议",,"NetWare 链路服务协议（NLSP）是 Novell NetWare 体系结构中的一种链路状态路由选择协议。 NLSP 基于 OSI 中间系统对中间系统（IS-IS） 协议，其设计目标是替换 IPX RIP（路由信息协议）和 SAP（服务通告协议）。Novell 公司最初开发的路由选择协议都是面向小型互连网络。","2"
"430","NMPI","NMPI",,"最为可怕的windows 9x漏洞。原来在windows 9x的NMPI协议中，有一个很大的bug，随时会令整个系统Buffer Overflow，同一时间可以将整个局域网的所有windows 9x电脑一齐炸死，真实惨不忍睹啊！","2"
"431","NNTP","网络新闻传输协议",,"网络新闻传输协议是一个主要用于阅读和张贴新闻文章(俗称为“帖子”，比较正式的是“新闻组邮件”)到Usenet上的Internet应用协议，也负责新闻在服务器间的传送","2"
"432","NOE",,,,"2"
"433","NONSTD","非标准协议",,,"2"
"434","NPMP","网络外设管理协议",,,"2"
"435","NS","NS协议",,,"2"
"436","NSIP","绿盟科技智能Profile漏洞识别技术",,"NSIP(NSFOCUS Intelligent Profile)是绿盟科技智能Profile漏洞识别技术的简称，该技术在国内甚至在国际上都是非常领先的漏洞识别技术，它在提高“绿盟远程安全评估系统”的扫描速度和准确率方面都起到了很大的促进作用。","2"
"437","NSRP","NetScreen Redundant Protocol",,"NSRP（NetScreen Redundant Protocol）是Juniper公司基于VRRP协议规范自行开发的设备冗余协议。防火墙作为企业核心网络中的关键设备，需要为所有进出网络的信息流提供安全保护，为满足客户不间断业务访问需求，要求防火墙设备必须具备高可靠性，能够在设备、链路及互连设备出现故障的情况下，提供网络访问路径无缝切换。NSRP冗余协议提供复杂网络环境下的冗余路径保护机制。","2"
"438","NTP","网络时间协议",,"NTP是网络时间协议(Network Time Protocol)，它是用来同步网络中各个计算机的时间的协议。","2"
"439","NULL","路由器的逻辑接口",,"null接口是一种纯软件性质的逻辑接口。它永远处于up状态，但不能转发数据包，也不能配置ip地址和链路层协议。如果在静态路由中指定到达某一网段的下一条为null接口时，则任何送到该网段的网络数据报文都会被丢弃，因此设备通过null接口提供了一种过滤报文的简单方法--将不需要的网络流量发送到null接口，从而免去配置acl（访问控制列表）的复杂工作。","2"
"440","Tencent_OICQ","OICQ协议",,"OICQ协议网络即时通讯软件, 网络寻呼机 (基于互联网的即时通信工具, 具有即时信息收发、网络寻呼、聊天、传输文件、手机短消息服务等功能, 对传统的无线寻呼和移动通讯进行增值服务）","2"
"441","OLD","小电流接地通信协议",,,"2"
"442","OLSR","优化链路状态路由协议",,"OLSR是Optimized Link State Routing 的简称，主要用于MANET网络(Mobile Ad hoc network)的路由协议。","2"
"443","OMAPI","OMAPI",,"是迅时通信OM系列IPPBX（以下简称“OM”）为第三方应用系统（以下简称“应用系统”） 提供的控制接口。通过该接口，OM将呼叫相关的事件、状态、统计信息以及录音包传送给应用系统；同时，应用系统可以向OM发出呼叫控制的请求、设置相关参数。","2"
"444","OMRON",,,,"2"
"445","OPENFLOW","OpenFlow协议",,"用来描述控制器和交换机之间交互所用信息的标准，以及控制器和交换机的接口标准。协议的核心部分是用于OpenFlow协议信息结构的集合。","2"
"446","OPENSAFETY","OPENSAFETY协议",,"openSAFETY，是世界上第一个100%开源的安全协议，首创开源、独立于总线的安全标准，适用于任何工业以太网方案。它不仅在法律层面上是开放的，在技术层面上也是。由于该协议独立于总线，openSAFETY可以应用于任何现场总线和工业以太网的方案中，甚至用于特殊定制的协议方案中。","2"
"447","OPENVPN","OpenVPN",,"VPN直译就是虚拟专用通道，是提供给企业之间或者个人与公司之间安全数据传输的隧道，OpenVPN无疑是Linux下开源VPN的先锋，提供了良好的性能和友好的用户GUI。","2"
"448","OPSI","OPSI协议",,,"2"
"449","OSI","开放式系统互联",,"OSI是Open System Interconnection的缩写，意为开放式系统互联。国际标准化组织（ISO）制定了OSI模型，该模型定义了不同计算机互联的标准，是设计和描述计算机网络通信的基本框架。OSI模型把网络通信的工作分为7层，分别是物理层、数据链路层、网络层、传输层、会话层、表示层和应用层","2"
"450","OSINLCP","OSINLCP协议",,,"2"
"451","OSPF","开放最短路径优先",,"OSPF(Open Shortest Path First开放式最短路径优先）是一个内部网关协议(Interior Gateway Protocol，简称IGP），用于在单一自治系统（autonomous system,AS）内决策路由。是对链路状态路由协议的一种实现，隶属内部网关协议（IGP），故运作于自治系统内部。著名的迪克斯加算法(Dijkstra)被用来计算最短路径树。OSPF分为OSPFv2和OSPFv3两个版本,其中OSPFv2用在IPv4网络，OSPFv3用在IPv6网络。OSPFv2是由RFC 2328定义的，OSPFv3是由RFC 5340定义的。与RIP相比，OSPF是链路状态协议，而RIP是距离矢量协议。","2"
"452","OUCH",,,,"2"
"453","P","P协议",,,"2"
"454","P1","P1协议",,,"2"
"455","P7","P7协议",,,"2"
"456","PACKETBB",,,,"2"
"457","PACKETLOGGER","PacketLogger",,"PacketLogger是一个插件，它可以打印系统所有进入和发出的消息，以便用于调试。它在必要地方解析消息，以表示消息是RPC还是一个时间戳。它也可以将数字的MessageID转换为对应的字符串。默认输出是由逗号分割文本，也可以作为CSV文件读取，在控制台中使用printf函数打印","2"
"458","PAGP","端口聚集协议",,"端口聚集协议（PAgP）(Port Aggregation Protocol)是Cisco的专有协议，帮助在快速以太通道链接中联系自动生成。PAgP是一种管理功能，它在链路的任一末端检查参数的一致性，并且有助于保持网络可用性。[1]  PAgP 分组为了商议一个通道的形成在快速以太通道可能的端口发送","2"
"459","PAP","口令验证协议",,"PAP 并不是一种强有效的认证方法，其密码以文本格式在电路上进行发送，对于窃听、重放或重复尝试和错误攻击没有任何保护。","2"
"460","PAPI","PAPI接口",,"PAPI是田纳西大学创新计算实验室开发的一组可以在多个处理器平台上对硬件性能计数器进行访问的标准接口，它的目标是方便用户在程序运行时监测和采集由硬件性能计数器记录的处理器事件信息。用户可以使用其提供的high/low api对程序某一段的使用时钟周期数，执行指令数，L1/L2 cache miss/access数，TLB miss数等等都统计出来，使用户能够直观的了解到程序的局部性如何。","2"
"461","PCAP","过程特性分析软件包",,"这个抓包库给抓包系统提供了一个高层次的接口。所有网络上的数据包，甚至是那些发送给其他主机的，通过这种机制，都是可以捕获的。它也支持把捕获的数据包保存为本地文件和从本地文件读取信息。","2"
"462","PCEP","PCEP",,,"2"
"463","PCLI","PCLI协议",,,"2"
"464","PCP","PCP",,"PCP，计算机术语，全称IP Payload Compression Protocol （IP载荷压缩协议，简称PCP），是一个减少IP数据报长度的协议。","2"
"465","PEEKREMOTE",,,,"2"
"466","PFLOG","PFLOG协议",,,"2"
"467","PGM","PGM",," PGM 是一种传输层多播协议,它使用协议号113直接运行在 IP 上。它没有对自己的消息或多播数据传输 使用 TCP 或 UDP","2"
"468","PGSQL","PgSQL",,"PostgreSQL是以加州大学伯克利分校计算机系开发的 POSTGRES 版本 4.2 为基础的对象关系型数据库管理系统（ORDBMS）。POSTGRES 领先的许多概念只是在非常迟的时候才出现在商业数据库中。","2"
"469","PIM","个人信息管理器",,"PIM，是英文缩写词，有多种含义，例如，PIM也有叫做PIMS的，英文为Personal Information Management System，中文叫做个人信息管理器。另指Protocol Independent Multicast PIM由IDMR（域间组播路由）工作组设计，PIM不依赖于某一特定单播路由协议，它可利用各种单播路由协议建立的单播路由表完成RPF检查功能，而不是维护一个分离的组播路由表实现组播转发。另外指一种IC卡，常用语小灵通通讯设备中等。","2"
"470","PINGPONG","PINGPONG协议",,,"2"
"471","PKT",,,,"2"
"472","PKTC","PKTC协议",,,"2"
"473","PN532","PN532",,"PN532是一个高度集成的非接触读写芯片，它包含80C51微控制器内核，集成了13.56MHz下的各种主动/被动式非接触通信方法和协议。","2"
"474","PNRP","对等名称解析协议",,"PNRP是由微软公司设计的基于IPv4和IPv6的点对点协议。它提供了安全灵活的动态名称注册和名称解析协议。和传统的DNS协议不同，它不需要一台域名服务器存储域名列表和对应的IP地址，而是利用了IPv6近乎无限的公网IP地址，采用点到点的方式解析域名，可以为任何一台电脑提供一个单独的域名，使你在任何地点都可以轻松访问你的电脑","2"
"475","MAIL_POP","邮局协议",,"POP的全称为 Post Office Protocol，即邮局协议，用于电子邮件的接收。本协议主要用于支持使用客户端远程管理在服务器上的电子邮件。","2"
"476","PPI","通讯协议",,"PPI，通讯协议。是西门子公司专为S7-200系列PLC开发的通讯协议。","2"
"477","WLANCAP","WLANCAP协议",,,"2"
"478","PPPMUX","PPPMUX协议",,,"2"
"479","PPPMUXCP","PPPMux控制协议",,,"2"
"480","PPPOED","PPPoED协议",,,"2"
"481","PPPOES","PPPoES协议",,,"2"
"482","PPTP","点对点隧道协议",,"PPTP（Point to Point Tunneling Protocol），即点对点隧道协议。该协议是在PPP协议的基础上开发的一种新的增强型安全协议，支持多协议虚拟专用网（VPN），可以通过密码验证协议（PAP）、可扩展认证协议（EAP）等方法增强安全性。可以使远程用户通过拨入ISP、通过直接连接Internet或其他网络安全地访问企业网。","2"
"483","PTP","图片传输协议",,"PTP是英语“图片传输协议(picture transfer protocol)”的缩写。PTP是最早由柯达公司与微软协商制定的一种标准，符合这种标准的图像设备在接入Windows XP系统之后可以更好地被系统和应用程序所共享，尤其在网络传输方面，系统可以直接访问这些设备用于建立网络相册时图片的上传、网上聊天时图片的传送等。","2"
"484","PTPIP","PTP/IPx协议",,"是一个通过IP连接，建立在 Picture Transfer Protocol (PTP) 上的传输层","2"
"485","PULSE","Pulse",,"Pulse是一款应用软件，支持Win9x/Me/NT/2000/XP/2003。","2"
"486","PVFS","虚拟文件系统",,"PVFS： Clemson 大学的并行虚拟文件系统（PVFS） 项目用来为运行 Linux 操作系统的 PC 群集创建一个开放源码的并行文件系统。PVFS 已被广泛地用作临时存储的高性能的大型文件系统和并行 I/O研究的基础架构。 作为一个并行文件系统，PVFS 将数据存储到多个群集节点的已有的文件系统中,多个客户端可以同时访问这些数据。","2"
"487","PW","伪线",,"伪线(PW)，通过信令或者静态配置实现伪线(PW)，通过信令或者静态配置实现。标签分发协议LDP、多协议边界网关协议MP-BGP4能够通过信令协议来传递双方的VC标签。伪线是有方向的，是一条从本地AC到对端AC之间的虚拟的、直接相连的数据通道，能够完成用户的二层数据透明传输；","2"
"488","Q931","Q.931协议",,"Q.931，作为电信体系的网络层协议，主要为ISDN 提供两设备间关于逻辑网络连接的呼叫建立、维护和终止等操作。Q.931是电信体系网络层（第三层）协议之一，由ITU Q 系列Q.930 文件详细说明。","2"
"489","Q932","Q.932协议",,,"2"
"490","Q933","Q.933协议",,,"2"
"491","QSIG","Q信令",,"QSIG：Q信令（Q Signaling ）QSIG：D－channel signaling protocol at Q reference point for PBX networking。","2"
"492","QUAKE","Quake",,"1996年6月22日，Quake共享版 (Quake Shareware) ——第一个真3D实时演算的FPS游戏发布。目前常被用于测试显卡的Quake是Quake3和Quake4，Quake3从99年开始就被用作测试显卡到现在了。Quake最成功的版本也是Quake3，也是电子竞技活动的开山鼻祖之一。","2"
"493","QUAKE2","Quake3游戏",,,"2"
"494","QUAKE3","Quake3游戏",,,"2"
"495","QUAKEWORLD","QUAKEWORLD协议",,,"2"
"496","QUIC","QUIC",,"QUIC（Quick UDP Internet Connection）是谷歌制定的一种基于UDP的低时延的互联网传输层协议。在2016年11月国际互联网工程任务组(IETF)召开了第一次QUIC工作组会议，受到了业界的广泛关注。这也意味着QUIC开始了它的标准化过程，成为新一代传输层协议","2"
"497","R3",,,,"2"
"498","RADIOTAP","Radiotap协议",,,"2"
"499","RADIUS","远程用户拨号认证服务",,"RADIUS：Remote Authentication Dial In User Service，远程用户拨号认证系统由RFC2865，RFC2866定义，是目前应用最广泛的AAA协议。AAA是一种管理框架，因此，它可以用多种协议来实现。在实践中，人们最常使用远程访问拨号用户服务（Remote Authentication Dial In User Service，RADIUS）来实现AAA。","2"
"500","RANAP","无线接入网络应用协议",,"无线接入网络应用协议（RANAP），用于RNC与核心网络的连接，它包括GSM系统BSSMAP。该协议的主要功能有：RAB管理、透明传输NAS消息流程、寻呼、安全模式控制和位置信息报告等。","2"
"501","RAW","RAM",,"RAM (Resource Access Management) 是阿里云提供的资源访问控制服务。通过RAM，您可以集中管理您的用户（比如员工、系统或应用程序），以及控制用户可以访问您名下哪些资源的权限。","2"
"502","RDP","远程显示协议",,"RDP，远程显示协议（Remote Display Protocol ）简称RDP。该协议是对国际电信联盟发布的一个国际标准的多通道会议协议T.120 的一个扩展。","2"
"503","RDT","可靠数据协议",,"发送方通过该协议把数据交给更底层（比如运输层交给网络层），底层负责传输，接收方再通过该协议把数据取出。我们把这个协议称作rdt(reliable data transfer)","2"
"504","REDBACK","Redback协议",,,"2"
"505","RELOAD","RELOAD",,"RELOAD（REsource LOcation And Discovery， RELOAD）协议，由IETF（Internet Engineering Task Force）P2PSIP（Peer-to-Peer Session Initiation Protocol）工作组指定。其核心成果，提供了统一的叠加网对等体和客户端协议，实现抽象的存储和消息路由服务。在很多问题上已经达成一致，可能于近期成为RFC。","2"
"506","RFC2190","RFC2190协议",,,"2"
"507","RIP","路由信息协议",,"路由信息协议RIP（Routing Information Protocol）是基于距离矢量算法的路由协议，利用跳数来作为计量标准。","2"
"508","RIPNG","RIPng",,"IETF在1997年为了解决RIP协议与IPv6的兼容性问题RIP协议进行了改进，制定了基于IPv6的RIPng(RIP next generation)标准，定义在RFC2080中。","2"
"509","RLOGIN","远程登录",,"远程登录（rlogin）是一个 UNIX 命令，它允许授权用户进入网络中的其它 UNIX 机器并且就像用户在现场操作一样。一旦进入主机，用户可以操作主机允许的任何事情，比如：读文件、编辑文件或删除文件等。Rlogin：远程登录命令 rlogin：Remote Login in Unix systems","2"
"510","RMCP","远程邮件检查协议",,"RMCP是通信网络协议的远程邮件检查协议之一。","2"
"511","RMI","RMI 传输协议",,,"2"
"512","RMP","RMP协议",,"针对组播通信协议中所使用的成员协议的伸缩性差的问题,提出了一种新的随机成员协议 (RMP).RMP通过使用随机的响应组成员的加入请求,建立一个每个节点仅仅维护logcN个其它成员信息的连接图,并可以为可靠的报文扩散提供基础. 文中对RMP的算法在数学上进行了分析,并通过仿真进行验证,结果表明,RMP是一种具有很强可伸缩性的成员协议","2"
"513","ROHC","Robust Header Compression健壮性包头压缩",,"ROHC是目前公认的应用于无线链路上较为理想的头部压缩方式，它有U／R／O三种工作模式，即单向模式、双向可信模式和最优化模式，每种模式下又有多种数据包。 ","2"
"514","ROOFNET",,,,"2"
"515","RPC","远程过程调用协议",,"RPC（Remote Procedure Call）—远程过程调用，它是一种通过网络从远程计算机程序上请求服务，而不需要了解底层网络技术的协议。RPC协议假定某些传输协议的存在，如TCP或UDP，为通信程序之间携带信息数据。在OSI网络通信模型中，RPC跨越了传输层和应用层。RPC使得开发包括网络分布式多程序在内的应用程序更加容易。","2"
"516","RPKIRTR",,,,"2"
"517","RPL","远程启动服务",,"是一种较早出现的无盘网络组建技术,无盘网络主要是指工作站其运行的操作系统，应用软件等文件都存储在服务器磁盘上的一种计算机网络构型。工作站上不具备磁盘驱动器（包括软盘，硬盘。光盘）仅是表面现象，不具有实质意思。","2"
"518","RSH","远程 shell",,"Rsh 是远程外壳(remote shell) 的缩写(外壳是操作系统的一种命令接口)。运行于远程计算机上的rshd 后台程序，接受rsh 命令，验证用户名和主机名信息，并执行该命令。当用户不愿或不需要与远程计算机建立远程会话时，可以使用rsh 工具执行输入的命令。Rsh 工具允许用户在远程计算机上执行单条命令，而无需在该远程计算机上进行登录。","2"
"519","RSIP",,,,"2"
"520","RSL","远程串行通讯",,,"2"
"521","RSP","RSP协议",,"RSP是一系列的基于GNU的嵌入式开发系统的一部分，作者提出了他自己使用GDB远程地调试嵌入式应用的一些论述。RSP是一种简单的基于ASCII编码的协议，它使用串口，局域网或者其它任何支持半双工数据交换的通讯方式。","2"
"522","RSVP","资源预留协议",,"RSVP，翻译成中文是请回复的意思，另一个意思是资源预留协议（Resource Reservation Protocol）的英文缩写。RSVP是一种位于第三层的信令协议，它独立于各种网络媒介，使得应用能将自己的QoS要求通过信令通知给网络，网络可以对此应用预留相应的资源。","2"
"523","RSYNC","rsync",,"rsync是linux系统下的数据镜像备份工具。使用快速增量备份工具Remote Sync可以远程同步，支持本地复制，或者与其他SSH、rsync主机同步。","2"
"524","RTACSER",,,,"2"
"525","RTCDC",,,,"2"
"526","RTCFG","RTcfg",,,"2"
"527","RTMAC","RTmac",,,"2"
"528","RTMPT","RTMPT",,"RTMPT协议基本上就是一个包装了RTMP的HTTP协议，它从客户端发送POST请求到服务器。由于HTTP 连接的非持久性本质，为了及时更新状态和数据，RTMPT需要客户端周期性向服务器轮询，取得服务器或者是其他客户端产生的通知事件。","2"
"529","VOIP_RTP","IP语音中的实时传输协议",,,"2"
"530","RTPPROXY","RTPPROXY",,"A high-performance software proxy that brings control to your VoIP network","2"
"531","RTSP","实时流传输协议",,"RTSP（Real Time Streaming Protocol），RFC2326，实时流传输协议，是TCP/IP协议体系中的一个应用层协议，由哥伦比亚大学、网景和RealNetworks公司提交的IETF RFC标准。该协议定义了一对多应用程序如何有效地通过IP网络传送多媒体数据。RTSP在体系结构上位于RTP和RTCP之上，它使用TCP或UDP完成数据传输。HTTP与RTSP相比，HTTP请求由客户机发出，服务器作出响应；使用RTSP时，客户机和服务器都可以发出请求，即RTSP可以是双向的。RTSP是用来控制声音或影像的多媒体串流协议，并允许同时多个串流需求控制，传输时所用的网络通讯协定并不在其定义的范围内，服务器端可以自行选择使用TCP或UDP来传送串流内容，它的语法和运作跟HTTP 1.1类似，但并不特别强调时间同步，所以比较能容忍网络延迟。而前面提到的允许同时多个串流需求控制（Multicast），除了可以降低服务器端的网络用量，更进而支持多方视讯会议（Video Conference）。因为与HTTP1.1的运作方式相似，所以代理服务器〈Proxy〉的快取功能〈Cache〉也同样适用于RTSP，并因RTSP具有重新导向功能，可视实际负载情况来转换提供服务的服务器，以避免过大的负载集中于同一服务器而造成延迟。","2"
"532","RUA",,,,"2"
"533","RUDP","可靠用户数据报协议",,"UDP/IP 协议中的 RUDP 是分层的并为虚拟连接提供可靠有序发送（直到重新发送的最大数目）。RUDP 设计灵活，便于多种传输层使用。传输电讯号协议就是其应用之一。","2"
"534","RX","RX协议",,,"2"
"535","S1AP","S1AP服务",,"S1AP提供E-UTRAN和演进型分组核心网之间的信令服务","2"
"536","S5066","S5066协议",,,"2"
"537","SABP","服务区域广播协议",,"服务区域广播协议(SABP)是一个针对信息广播服务的 3G UMTS 协议，其允许蜂窝运行商来递送信息，例如，股票价格、交通信息，天气预报和突发事件警报，给在被选网络单元的移动用户。中国标准为 1492/07-155YD/T1374.7-2007 2GHz TD-SCDMA/WCDMA 数字蜂窝移动通信网Iu接口技术要求(第二阶段) 第7部分：服务区广播协议(SABP)","2"
"538","SAMETIME","Sametime",,"IBM公司的Sametime就是一款知名的企业级即时通信软件，通常部署在大中型企业中以提高员工实时沟通的能力。事实上，IBM公司自身就使用这个软件来支撑全球40万职工之间实时的交流和沟通。","2"
"539","SAP","会话通知协议",,"SAP 收听方通过组播范围区域通知协议（或其它协议）知道通知所在的组播范围，并收听那些范围内的 SAP 地址和端口上的通知。如此，收听方最终会知道所有会话通知，并允许那些会话加入组播范围。","2"
"540","SASP","SASP",,,"2"
"541","SBC","会话边界控制器",,"Session Border Controller，即会话边界控制器SBC已经逐渐成为NGN和IMS网络的标准配置产品（如同Lanswitch和路由器）。也被广泛称为BAC（边界接入控制器），定位在IMS网络的ABG (access border gateway) ，解决NGN业务部署中遇到的NAT/FW穿越、安全、互通、QoS等问题。","2"
"542","SBUS","s-bus",,"S-bus为futaba使用的串行通信协议。实际上为串口通信。","2"
"543","SCCP","信令连接控制协议 ",,"信令连接控制协议 SCCP 是用于思科呼叫管理及其 VOIP 电话之间的思科专有协议。为解决 VOIP 问题，要求 LAN 或者基于 IP 的 PBX 的终点站操作简单，常见且相对便宜。相对于 H.323 推荐的相当昂贵的系统而言，SCCP 定义了一个简单且易于使用的结构。","2"
"544","SCCPMG","SCCPMG协议",,,"2"
"545","SCOP","SCOP协议",,,"2"
"546","SCTP","流控制传输协议",,"SCTP（Stream Control Transmission Protocol，流控制传输协议）是IETF（Internet Engineering Task Force，因特网工程任务组）在2000年定义的一个传输层（Transport Layer）协议，是提供基于不可靠传输业务的协议之上的可靠的数据报传输协议。SCTP的设计用于通过IP网传输SCN（Signaling Communication Network，信令通信网）窄带信令消息。后期广泛用于EPC网络中的S6a/S1/Sgs/Sv等接口中。","2"
"547","SDH","SDH",,"SDH（Synchronous Digital Hierarchy，同步数字体系），根据ITU-T的建议定义，是不同速度的数位信号的传输提供相应等级的信息结构，包括复用方法和映射方法，以及相关的同步方法组成的一个技术体制。","2"
"548","SDLC","同步数据链路控制",,"是IBM公司在七十年代初为满足用户需要以环路方式配置设备而设计推出的一种通讯协议，目前广泛应用与我过的pos终端。","2"
"549","SDP","SDP协议",,"SDP 完全是一种会话描述格式 ― 它不属于传输协议 ― 它只使用不同的适当的传输协议，包括会话通知协议（SAP）、会话初始协议（SIP）、实时流协议（RTSP）、MIME 扩展协议的电子邮件以及超文本传输协议（HTTP）。SDP协议是也是基于文本的协议，这样就能保证协议的可扩展性比较强，这样就使其具有广泛的应用范围。SDP 不支持会话内容或媒体编码的协商，所以在流媒体中只用来描述媒体信息。媒体协商这一块要用RTSP来实现．","2"
"550","SEBEK","Sebek协议",,"Sebek是一个数据捕获工具。这种捕获工具是不容易被攻击者发现的。所有数据捕获工具的目的都是用捕获的数据准确的给我们重现蜜罐上的事件。","2"
"551","SELFM",,,,"2"
"552","SERCOSIII","串行实时通信协议",,"SERCOS(serial real time communication specification,串行实时通信协议)是一种用于数字伺服和传动系统的现场总线接口和数据交换协议，能够实现工业控制计算机与数字伺服系统、传感器和可编程控制器I/O口之间的实时数据通讯，也可以理解为是一个开放的智能控制、数字化驱动接口是用于高速串联的，闭环数据在光纤上进行实时通信的接口。","2"
"553","SFLOW","sflow",,"sFlow 是由InMon、HP 和FoundryNetworks 于2001 年联合开发的一种网络监测技术，它采用数据流随机采样技术，可提供完整的第二层到第四层，甚至全网络范围内的流量信息，可以适应超大网络流量(如大于10Gbit/s)环境下的流量分析，让用户详细、实时地分析网络传输流的性能、趋势和存在的问题。","2"
"554","SGSAP","SGSAP",,,"2"
"555","SIGCOMP","信令压缩协议",,,"2"
"556","SIMULCRYPT","SimulCryp协议",,"SimulCrypt使用多个机顶盒，每个使用不同的CA系统，用来验证节目播放。每个CA系统需要的不同的ECM和EMM同时传送，每个机顶盒识别和使用适当的ECM和EMM用来验证。ATSC标准使用SimulCrypt。MultiCrypt允许多个CA系统使用同一个机顶盒，此机顶盒使用为每个CA系统嵌入的智能卡组成的PC卡，每个卡插入机顶盒的插槽中，每个卡识别ECM和EMM用于验证。","2"
"557","SIP","会话发起协议",,"SIP（Session Initiation Protocol，会话初始协议）是由IETF（Internet Engineering Task Force，因特网工程任务组）制定的多媒体通信协议。它是一个基于文本的应用层控制协议，用于创建、修改和释放一个或多个参与者的会话。广泛应用于CS（Circuit Switched，电路交换）、NGN（Next Generation Network，下一代网络）以及IMS（IP Multimedia Subsystem，IP多媒体子系统）的网络中，可以支持并应用于语音、视频、数据等多媒体业务，同时也可以应用于Presence（呈现）、Instant Message（即时消息）等特色业务。可以说，有IP网络的地方就有SIP协议的存在","2"
"558","SITA",,,,"2"
"559","SKINNY","SKINNY协议",,,"2"
"560","SLARP","Serial Line Address Resolution Protocol",,"SLARP，Serial Line Address Resolution Protocol，简单的说，就是串口链路上用来检测邻居的协议，定义在HDLC上，Cisco私有。","2"
"561","SLIMP3","SLIMP3协议",,,"2"
"562","SSL","SSL安全协议",,"SSL安全协议是国际上通行的银行卡密码校验技术和标准之一，又称为“安全套接层”（Secure Sockets Layer）协议，是Netscape Communication公司1996年设计开发的，主要用于提高应用程序之间的数据安全系数。","2"
"563","SLOW","慢速协议",,"慢速协议（-slowprotocol），计算机技术术语，慢速协议有三种，包括802.3ah OAM、LACP协议和Marker协议。","2"
"564","SLSK","slsk",,"Soulseek简称slsk,是目前一款非常优秀且热门的Mp3下载p2p软件，被颜峻先生极其生动的翻译成“搜魂”。该软件始于2001年，在2003年被国内广泛使用，大家可以找到各种音乐，不论主流或偏门","2"
"565","SMB","SMB协议",,"SMB（Server Message Block）通信协议是微软（Microsoft）和英特尔(Intel)在1987年制定的协议，主要是作为Microsoft网络的通讯协议。SMB 是在会话层（session layer）和表示层（presentation layer）以及小部分应用层（application layer）的协议。","2"
"566","SML","SML",,"SML（Smart Message Language）是近几年由国际上几家知名电力公司共同制定的通讯协议，并即将成为国际标准体系IEC62056的新成员。它通过SML文件的方式实现通讯，更加的灵活简便，并成功运用于智能电网系统架构AMI（Advanced Metering Infrastructure）之中，为智能电网提供了一套新的解决方案。","2"
"567","SMRSE","SMRSE协议",,,"2"
"568","MAIL_SMTP","电子邮件协议",,"SMTP的全称是“Simple Mail Transfer Protocol”，即简单邮件传输协议。它是一组用于从源地址到目的地址传输邮件的规范，通过它来控制邮件的中转方式。SMTP 协议属于TCP/IP协议簇，它帮助每台计算机在发送或中转信件时找到下一个目的地。SMTP 服务器就是遵循SMTP协议的发送邮件服务器。SMTP认证，简单地说就是要求必须在提供了账户名和密码之后才可以登录 SMTP 服务器，这就使得那些垃圾邮件的散播者无可乘之机。增加 SMTP 认证的目的是为了使用户避免受到垃圾邮件的侵扰","2"
"569","SMUX","SMUX",,"SMUX是实现SNMP代理和用户进程通信的协议","2"
"570","SNA","多协议传输网 ",,"IBM 系统网络体系结构 （IBM Systems Network Architecture）SNA是IBM公司开发的网络体系结构，在IBM公司的主机环境中得到广泛的应用。一般来说，SNA主要是IBM公司的大型机（ES/9000、S/390等）和中型机（AS/400）的主要联网协议。SNA的历史早在1974年首次公布的SNA是IBM为了连接他的3270系列产品而推出的方案。","2"
"571","SNAETH","SNAETH",,,"2"
"572","SNDCP","子网相关会聚协议",,"SNDCP位于网络层的下面，逻辑链路控制层上面，它存在于MS和SGSN。","2"
"573","SNMP","简单网络管理协议 ",,"简单网络管理协议（SNMP），由一组网络管理的标准组成，包含一个应用层协议（application layer protocol）、数据库模型（database schema）和一组资源对象。该协议能够支持网络管理系统，用以监测连接到网络上的设备是否有任何引起管理上关注的情况。该协议是互联网工程工作小组（IETF，Internet Engineering Task Force）定义的internet协议簇的一部分。SNMP的目标是管理互联网Internet上众多厂家生产的软硬件平台，因此SNMP受Internet标准网络管理框架的影响也很大。SNMP已经出到第三个版本的协议，其功能较以前已经大大地加强和改进了","2"
"574","SOCKETCAN","socketcan",,"socketcan子系统是在Linux下CAN协议(Controller Area Network)实现的一种实现方法。","2"
"575","SOCKS","防火墙安全会话转换协议 ",,"SOCKS：防火墙安全会话转换协议 （Socks: Protocol for sessions traversal across firewall securely） SOCKS 协议提供一个框架，为在 TCP 和 UDP 域中的客户机/服务器应用程序能更方便安全地使用网络防火墙所提供的服务。协议工作在OSI参考模型的第5层(会话层)，使用UDP协议传输数据，因而不提供如传递 ICMP 信息之类的网络层网关服务。","2"
"576","SOUPBINTCP",,,,"2"
"577","SPP","标准并行接口",,"标准并行接口（Standard Parallel Port）。它可以提供50K Bits/秒的典型传输速度，其最高的传输速度可达150K Bits/秒。可进行9Bits的并行输入（现在的板载并行接口一般支持数据口的双向传输故最大可达17Bits的输入）和12Bits的并行输出。通常可选择Nibble（4bits）或Byte（8bits）的方式进行输入数据，还有一种Bi-directional的双向传输方式，这种方式需硬件支持。SPP硬件是由8条数据线，4条控制线和5条状态线所组成，它们分别对应三个不同的寄存器来进行数据的读写操作。","2"
"578","SRVLOC","SRVLOC",,"服务定位","2"
"579","SSCOP","SSCOP",,"SSCOP是面向连接的协议，它的基本功能是保证数据可靠传输，具体包括流控、发送帧编序、错误检测和错误恢复，它的主要内部机制是错误恢复时采用选择性重传及相应的流控。","2"
"580","SSH","安全外壳协议",,"SSH 为 Secure Shell 的缩写，由 IETF 的网络小组（Network Working Group）所制定；SSH 为建立在应用层基础上的安全协议。SSH 是目前较可靠，专为远程登录会话和其他网络服务提供安全性的协议。利用 SSH 协议可以有效防止远程管理过程中的信息泄露问题。SSH最初是UNIX系统上的一个程序，后来又迅速扩展到其他操作平台。SSH在正确使用时可弥补网络中的漏洞。SSH客户端适用于多种平台。几乎所有UNIX平台—包括HP-UX、Linux、AIX、Solaris、Digital UNIX、Irix，以及其他平台，都可运行SSH。","2"
"581","SSPROTOCOL",,,,"2"
"582","STANAG4607","STANAG4607",,"STANAG是Standardization Agreement即“标准化协议”的缩写，建立的目的是为北约成员国之间提供一套军事和技术上的标注化程序","2"
"583","STUN","Simple Traversal of UDP over NATs",,"STUN（Simple Traversal of UDP over NATs，NAT 的UDP简单穿越）是一种网络协议，它允许位于NAT（或多重NAT）后的客户端找出自己的公网地址，查出自己位于哪种类型的NAT之后以及NAT为某一 个本地端口所绑定的Internet端端口。这些信息被用来在两个同时处于NAT 路由器之后的主机之间建立UDP通信。该协议由RFC 3489定义。目前RFC 3489协议已被RFC 5389协议所取代，新的协议中，将STUN定义为一个协助穿越NAT的工具，并不独立提供穿越的解决方案。它还有升级版本RFC 7350，目前正在完善中。","2"
"584","SUA","SUA",,"SUA：SCCP用户的适配层协议，它的主要功能是，适配传送SCCP的用户信息给IP数据库，提供SCCP的网管互通功能。大写的话，是ZPW—2000A轨道电路中的电气绝缘节中的一部分。","2"
"585","SV","SV",,,"2"
"586","SYMANTEC","软件授权许可协议",,,"2"
"587","SYNERGY","SYNERGY",,"Synergy 是一款能够让使用者仅用一套键盘鼠标，就同时操控多部计算机的免费工具软件。使用者能够在包括 Windows 、 Linux 、 Mac OS 等不同的系统上安装它，并且在设定好主从关系后，就能够透过一组键盘鼠标来进行多部计算机的操控。","2"
"588","SYNPHASOR",,,,"2"
"589","SYSLOG","SYSLOG",,"在Unix类操作系统上，syslog广泛应用于系统日志。syslog日志消息既可以记录在本地文件中，也可以通过网络发送到接收syslog的服务器。接收syslog的服务器可以对多个设备的syslog消息进行统一的存储，或者解析其中的内容做相应的处理。常见的应用场景是网络管理工具、安全管理系统、日志审计系统。","2"
"590","T124","T.124",,"总体会议控制：规定了会议的各种管理机制。","2"
"591","T38","T38",,"T38 是一个关于如何通过计算机网络收发传真的协议。因为无法象语音通话那样在计算机数据网络中发送传真数据，因此需要T38。","2"
"592","Tacacs+","终端访问控制器访问控制系统 ",,"TACACS+(Terminal Access Controller Access Control System)终端访问控制器访问控制系统。与我们IDsentrie的Radius协议相近。不过TACACS+用的是TCP协议，Radius用的是UDP,不知道各有什么优点和缺点呢。它们的重要作用就是3A。 所谓3A, 即Authentication认证，Authorization授权， Accounting计费。 在测试Radius时，我对authentication已经比较了解，但是对authorization还比较模糊，这次测试tacacs+,使我对authorization也开始了解了。授权简单的说就是给用户开放某些资源","2"
"593","TACPLUS",,,,"2"
"594","TAPA",,,,"2"
"595","TCAP","事务处理能力应用部分",,"事务处理能力应用部分（TCAP）是 SS7 协议之一，能够部署高级智能网络服务，这是通过使用信令连接控制部分（SCCP：Signaling Connection Control Part）无连接服务，在信令点间提供非电路相关（non-circuit-related）信息的交换来实现的。此外，TCAP 也支持远程控制功能 － 调用其它远程网络交换机上的性能特征。","2"
"596","9P","9P协议",,,"2"
"597","TCPENCAP","TCPENCAP",,"封装标头","2"
"598","TDMOE","TDMOE",,"TDMoE技术的出现是和以太网的飞速发展分不开的。在数据链路级，以太网是最流行的网络技术，它可以用来传送IP分组。超过百分之八十的局域网采用以太网技术。以太网之所以如此流行，是因为它的传输数率高、成本低、容易布署、技术相对简单等等。以太网可以支持各种虚拟的网络协议，这一点使得以太网成为大多数计算机用户首选的理想网络技术。专家认为，基于IP的以太网，将是未来宽带网络的主要架构，而且传统的电信业务也将向这一网络迁移。正是由于以上原因使得TDM over Ethernet技术应运而生。推动这一技术发展的主要驱动因素是成本和利润。这是因为业务提供商希望通过无处不在的、廉价的以太网来向用户提供TDM业务，从而降低成本最大限度的实现利润。  所谓TDM over Ethernet技术，就是通过Ethernet透明的传送TDM业务（比如话音、图像和数据业务）。 其基本原理就是将TDM数据不做任何翻译和解释，封装为以太网数据包，然后通过基于分组交换的以太网传送到目的端，目的端需要将收到的数据包打开并恢复出原始的TDM数据流。对于用户而言，不需要考虑中间的传输媒介，相当于为用户提供了一条透明的TDM通道。正是基于这个原因，分组网络上的电路仿真又称之为通道仿真。 2．2 TDMoE与TDMoIP的比较 与TDMoE技术相近的是TDMoIP技术，即：TDM over IP。二者的主要不同之处在于封装格式上，TDMoE的数据包只需封装到二层（数据链路层），而TDMoIP的数据包需要封装到三层（网络层），因此TDMoE包的封装效率更高。从传输效率上来看，由于TDMoIP分组的报头开销比较大，故传输效率低于前者，所占用的带宽也较大。比如采用IP/UDP/RTP/TDM的封装格式，需要大概40字节的开销（20字节的IP报头，12字节的RTP报头和8字节的UDP报头）。如果一个IP分组只用来传送一个E1帧（32个字节）或T1帧（24字节），40字节的报头开销就太浪费了。可以通过对报头开销的压缩或把多个E1/T1帧组装成一个“超帧”的办法来提高TDMoIP的传输效率。从应用的角度看，二者的应用范围不同，TDMoIP可以和以太网相结合，因此TDMoIP的应用范围更广，但技术也更复杂一些。目前的以太网局域网的应用广泛程度远高于经过路由器的复杂IP网络。因此，随着以太网的普及和发展，TDMoE技术将会得到更广泛的应用。 2．3 TDMoE和VoIP、ATM的比较 TDMoE相对于VoIP更简单、廉价。这是因为TDMoE对话音、数据、信令及协议是透明的，不做任何的翻译和修改。而VoIP需要在信令格式间采用新的协议和翻译，相对于TDMoE，","2"
"599","TDS","TDS",,"TDS是Sqlserver的通讯协议。","2"
"600","TEIMANAGEMENT",,,,"2"
"601","TELKONET",,,,"2"
"602","Telnet","远程终端协议",,"Telnet协议是TCP/IP协议族中的一员，是Internet远程登陆服务的标准协议和主要方式。它为用户提供了在本地计算机上完成远程主机工作的能力。在终端使用者的电脑上使用telnet程序，用它连接到服务器。终端使用者可以在telnet程序中输入命令，这些命令会在服务器上运行，就像直接在服务器的控制台上输入一样。可以在本地就能控制服务器。要开始一个telnet会话，必须输入用户名和密码来登录服务器。Telnet是常用的远程控制Web服务器的方法。","2"
"604","TETRA","TETRA",,,"2"
"605","TFP",,,,"2"
"606","TFTP","简单文件传送协议",,"TFTP（Trivial File Transfer Protocol,简单文件传输协议）是TCP/IP协议族中的一个用来在客户机与服务器之间进行简单文件传输的协议，提供不复杂、开销不大的文件传输服务。端口号为69。","2"
"607","TIME","时间协议",,,"2"
"608","TIPC","TIPC",,"TIPC是爱立信公司提出的一种透明进程间通信协议, 主要适用于高可用(HAL)和动态集群环境. 该软件当前主要由风河(windriver)公司在维护, 主要支持Linux, Solaris 和 VxWorks三种操作系统, 从Linux内核2.6.34开始支持TIPC的最新版本2.0, 不过还有很多协议设计的功能没有实现. 在可信网络环境下, TCP/IP协议的很多操作是冗余的, 例如, 著名的三次握手, 从而导致通信效率下降, 增加了应用程序的通信时间, 不利于对时间响应要求比较高的应用, 比如, 处理集群成员节点由于重启, down机等各种原因导致的增加和减少. TIPC针对可信网络环境, 减少了建立通信连接的步骤和寻址目标地址的操作(在TCP/IP协议里, 完成这些操作节点间最少也需要9次包交换, 而使用TIPC则可以减少到2次). 这可以提高节点间信息交换的频率以及减少节点间等待的时间.","2"
"609","TIVOCONNECT",,,,"2"
"610","TNEF","Transport Neutral Encapsulation Format",,"Microsoft Outlook和Microsoft Exchange Server的专有邮件附件格式。 用TNEF编码附加的邮件最常见文件名为Winmail.dat或win.dat，MIME类型是Application/ MS-TNEF。 但官方（IANA）的媒体类型是Application/ vnd.ms-TNEF。","2"
"611","ORACLE_TNS","透明网络底层",,"Oracle中TNS的完整定义：Transparence Network Substrate，透明网络底层。监听服务是它重要的一部分，不是全部，不要把TNS当作只是监听器。","2"
"612","TPCP","TPCP协议",,,"2"
"613","TPKT","应用程数据传输协议",,"应用程数据传输协议，用来传输应用层数据负载。","2"
"614","TPNCP","TrunkPack网络控制协议",,,"2"
"615","TR","TR",,"TR-069是由DSL论坛（www.dslforum.org）所开发的技术规范之一，其全称为“CPE广域网管理协议”。它提供了对下一代网络中家庭网络设备进行管理配置的通用框架和协议，用于从网络侧对家庭网络中的网关、路由器、机顶盒等设备进行远程集中管理。","2"
"616","TRILL","多链接透明互联",,"在交换方面，IETF正在致力于多链接半透明互联(TRILL)标准的研究。IETF打算用该标准克服生成树协议(STP)在规模上和拓扑重聚方面存在的不足。在路由方面，IETF正在致力于制订位置标识与身份标识分离协议(LISP)。IETF计划为与多个ISP服务提供商合作的企业改进寻址和负载平衡。","2"
"617","TS2",,,,"2"
"618","TSP","时间戳协议",,"TSP是简单的请求/响应协议。请求时间戳的实体发 送一个TimeStampReq消息给TSA，以请求一个时间戳。TimeStampReq消息包含待加戳的数据的散列值，TSA在 TimeStampReq消息中传回时间戳。TimeStampReq消息包含请求的状态（即被批准或拒绝）及时间戳，时间戳遵循CMS中的签名消息格 式。","2"
"619","TTE","时间触发网络",,"时间触发网络（TTE：Time Trigger Ethernet）是一种具有高速、高可靠、高时实和高兼容等特性的新型网络技术，是目前航电总线的主流技术。TTE 网络的核心基础是时间同步算法与调度、千兆以太网物理层收发芯片及交换芯片的实现","2"
"620","TZSP","TZSP协议",,,"2"
"621","UA","用户代理",,"用户代理(User Agent).UA是SIP协议中的一个逻辑实体,SIP是一个信令协议,代理的含义为代替用户处理信令协议,简单说就是替用户收发信令信息.其物理实体可以是SIP终端(如SIP软,硬电话终端,SIP多媒体终端等),基于SIP的接入网元,IAD以及SIP网络中的其它网元等设备.除此以外SIP系统中用来处理信令的还有代理服务器(PROXY),重定向服务器(RS)等逻辑实体.","2"
"622","UA3G","3g代理",,,"2"
"623","UAUDP","UDP用户代理",,,"2"
"624","UDLD","单向链路检测",,"UDLD （UniDirectional Link Detection 单向链路检测）：是个Cisco私有的二层协议，用于监听利用光纤或双绞线连接的以太链路的物理配置，当出现单向链路（只能向一个方向传输，比如我能把数据发给你，你也能收到，但是你发给我的数据我收不到）时，UDLD可以检测出这一状况，关闭相应接口并发送警告信息。单向链路可能引起很多问题，尤其是生成树，可能会造成回环。注意：UDLD需要链路两端设备都支持才能正常运行。","2"
"625","ARP","地址解析协议",,"地址解析协议，即ARP（Address Resolution Protocol），是根据IP地址获取物理地址的一个TCP/IP协议。主机发送信息时将包含目标IP地址的ARP请求广播到网络上的所有主机，并接收返回消息，以此确定目标的物理地址；收到返回消息后将该IP地址和物理地址存入本机ARP缓存中并保留一定时间，下次请求时直接查询ARP缓存以节约资源。地址解析协议是建立在网络中各个主机互相信任的基础上的，网络上的主机可以自主发送ARP应答消息，其他主机收到应答报文时不会检测该报文的真实性就会将其记入本机ARP缓存；由此攻击者就可以向某一主机发送伪ARP应答报文，使其发送的信息无法到达预期的主机或到达错误的主机，这就构成了一个ARP欺骗。ARP命令可用于查询本机ARP缓存中IP地址和MAC地址的对应关系、添加或删除静态对应关系等。相关协议有RARP、代理ARP。NDP用于在IPv6中代替地址解析协议。","2"
"626","UDPENCAP","UDPENCAP协议",,,"2"
"627","UHD","UHD",,"UHD是”超高清“的意思UHD的应用在电视机技术上最为普遍，目前已有不少厂商推出了UHD超高清电视。","2"
"628","ULP","上层协议",,,"2"
"629","UMA_TCP",,,,"2"
"630","USB","通用串行总线",,"USB，是英文Universal Serial Bus（通用串行总线）的缩写，是一个外部总线标准，用于规范电脑与外部设备的连接和通讯。是应用在PC领域的接口技术。USB接口支持设备的即插即用和热插拔功能。USB是在1994年底由英特尔、康柏、IBM、Microsoft等多家公司联合提出的。","2"
"631","USER","用户",,"User [英][ˈju:zə(r)] [美][ˈjuzɚ] 简明释义: n.用户，使用者；吸毒成瘾者。在计算机中，User则是让普通用户使用的帐户。它的级别和权限是介于“Administrator”和“Guest”之间的，即是这个帐户没有修改系统设置和进行安装程序的权限，但拥有创建修改任何文档的权限。","2"
"632","V5EF","V5EF协议",,,"2"
"633","V5UA","V5UA协议  ",,,"2"
"634","VICP","VICP",,,"2"
"635","VINES","VINES",,"Banyan公司的Vines 6.0 是颇有特点的网络操作系统，它通过精心设计的Street Talk Ⅲ提供了当时最强大","2"
"636","VLAN","vlan协议","基础协议",,"2"
"637","VMLAB","VMLAB",,"VMLAB Vmlab是AVR系列单片机的一个纯软件模拟仿真工具。从V3.12开始，它变成了免费的版本。Vmlab还能仿真ST62系列的单片机","2"
"638","VNC","虚拟网络控制台",,"VNC (Virtual Network Console)是虚拟网络控制台的缩写。它 是一款优秀的远程控制工具软件，由著名的 AT&T 的欧洲研究实验室开发的。VNC 是在基于 UNIX 和 Linux 操作系统的免费的开源软件，远程控制能力强大，高效实用，其性能可以和 Windows 和 MAC 中的任何远程控制软件媲美。 在 Linux 中，VNC 包括以下四个命令：vncserver，vncviewer，vncpasswd，和 vncconnect。大多数情况下用户只需要其中的两个命令：vncserver 和 vncviewer。","2"
"639","VNTAG",,,,"2"
"640","VRRP","虚拟路由器冗余协议",,"虚拟路由冗余协议(Virtual Router Redundancy Protocol，简称VRRP)是由IETF提出的解决局域网中配置静态网关出现单点失效现象的路由协议，1998年已推出正式的RFC2338协议标准。VRRP广泛应用在边缘网络中，它的设计目标是支持特定情况下IP数据流量失败转移不会引起混乱，允许主机使用单路由器，以及及时在实际第一跳路由器使用失败的情形下仍能够维护路由器间的连通性。","2"
"641","VRT",,,,"2"
"642","VSNCP",,,,"2"
"643","VSNP",,,,"2"
"644","VTP","VLAN中继协议",,"VTP（VLAN Trunking Protocol）：是VLAN中继协议，也被称为虚拟局域网干道协议。它是思科私有协议。作用是十几台交换机在企业网中，配置VLAN工作量大，可以使用VTP协议，把一台交换机配置成VTP Server, 其余交换机配置成VTP Client,这样他们可以自动学习到server 上的VLAN 信息。","2"
"645","VUZE","vuze",,"《vuze》是The Vuze Team开发的BitTorrent客户端。","2"
"646","WAI","网页易读性倡议",,"网页易读性倡议的作品与世界各地的组织，以制定战略，方针和资源，以帮助使网站方便残疾人士使用。","2"
"647","WASSP",,,,"2"
"648","WCCP","网页缓存通信协议",,"这个协议能让一台路由器与你的缓存服务器对话。它们谈论的是这台路由器应该拦截什么类型的通信并且通过一个 GRE 隧道重新引导到缓存。WCCP 第二版支持身份识别等安全措施，支持多台路由器、缓存路由器和许多协议。因此，除了 WWW HTTP 之外，你还可以做文件服务器和其它类型的缓存。","2"
"649","WCP","WCP协议",,,"2"
"650","WFLEET",,,,"2"
"651","WHO","WHO协议",,,"2"
"652","WHOIS","域名查询协议",,"whois（读作“Who is”，非缩写）是用来查询域名的IP以及所有者等信息的传输协议。简单说，whois就是一个用来查询域名是否已经被注册，以及注册域名的详细信息的数据库（如域名所有人、域名注册商）。通过whois来实现对域名信息的查询。早期的whois查询多以命令列接口存在，但是现在出现了一些网页接口简化的线上查询工具，可以一次向不同的数据库查询。网页接口的查询工具仍然依赖whois协议向服务器发送查询请求，命令列接口的工具仍然被系统管理员广泛使用。whois通常使用TCP协议43端口。每个域名/IP的whois信息由对应的管理机构保存。","2"
"653","WINSREPL",,,,"2"
"654","WLCCP",,,,"2"
"656","WOW","魔兽世界",,"《魔兽世界》（World of Warcraft）是由著名游戏公司暴雪娱乐所制作的第一款网络游戏，属于大型多人在线角色扮演游戏。游戏以该公司出品的即时战略游戏《魔兽争霸》的剧情为历史背景，依托魔兽争霸的历史事件和英雄人物，魔兽世界有着完整的历史背景时间线。[1]  玩家在魔兽世界中冒险、完成任务、新的历险、探索未知的世界、征服怪物等。","2"
"657","WRETH",,,,"2"
"658","WSMP","WSMP",,,"2"
"659","WSP","无线会话协议",,"WSP是在无线应用协议（WAP：Wireless Application Protocol ）组中的协议，用两种服务提供无线应用环境一个稳定的接口。","2"
"660","WTLS","无线安全传输层",,"WAP的通讯协议下的四个层次之一。WTLS 是根据工业标准TLS Protocol而制定的安全协议。WTLS是设计使用在传输层（Transport Layer）之上的安全层（Security Layer），并针对较小频宽的通讯环境作修正。WTLS的功能类似全球信息网站所使用的SSL加密传输技术，WTLS可以确保资料在传输的过程中经过编码、加密处理，以避免骇客在数据传输过程中窃取保密性数据。","2"
"661","WTP","WTP",,"WTP WAP传输协议层，提供传输支持，增加由WDP提供的数据报服务的可靠性。","2"
"662","X11","X11协议",,"X11也叫做X Window系统，X Window系统 (X11或X)是一种 位图 显示的 视窗系统 。它是在 Unix 和 类Unix 操作系统 ，以及 OpenVMS 上建立图形用户界面 的标准工具包和协议，并可用于几乎所有已有的现代操作系统。","2"
"663","X25","X25协议",,"X.25协议是一个广泛使用的协议，它由ITU-T提出，是面向计算机的数据通讯网[1]  ，它由传输线路、分组交换机、远程集中器和分组终端等基本设备组成。","2"
"664","X29","X29协议",,,"2"
"665","X2AP","X2AP协议",,,"2"
"666","XDMCP","X显示监控协议",,"基于XDMCP的远程X是非常简单易用而且体现Unix/Linux长处的一个网络应用，它是把整个X 桌面输出到远端.比如通过XDMCP你可以把一台淘汰了的486转换成能运行大型科学计算软件的X终端。","2"
"667","XMCP","XMCP",,,"2"
"668","XML","可扩展标记语言",,"可扩展标记语言，标准通用标记语言的子集，是一种用于标记电子文件使其具有结构性的标记语言。","2"
"669","XMPP","可扩展通讯和表示协议",,"XMPP是一种基于标准通用标记语言的子集XML的协议，它继承了在XML环境中灵活的发展性。因此，基于XMPP的应用具有超强的可扩展性。经过扩展以后的XMPP可以通过发送扩展的信息来处理用户的需求，以及在XMPP的顶端建立如内容发布系统和基于地址的服务等应用程序。而且，XMPP包含了针对服务器端的软件协议，使之能与另一个进行通话，这使得开发者更容易建立客户应用程序或给一个配好系统添加功能","2"
"670","Cisco_X25",,,,"2"
"671","XTP","快速传送协议",,,"2"
"672","XYPLEX","XYPLEX",,,"2"
"673","YAMI","YAMI",,,"2"
"674","ZBEE","ZBEE",,,"2"
"675","ZEBRA","ZEBRA",,,"2"
"676","ZEP","ZEP",,,"2"
"677","ZEP","ZEP",,,"2"
"678","TLS","安全传输层协议",,"安全传输层协议（TLS）用于在两个通信应用程序之间提供保密性和数据完整性。该协议由两层组成： TLS 记录协议（TLS Record）和 TLS 握手协议（TLS Handshake）。","2"
"679","HOPOPTS","HOPOPTS",,,"2"
"680","GGP","GGP",,"核心网为了正确和高效地路由报文需要知道Internet其他部分发生的情况，包括路由信息和子网特性。当一个网关处理重负载而使速度特别慢，并且这个网关是访问子网的唯一途径时，通常使用这种类型的信息，网络中的其他网关能剪裁交通流量以减轻网关的负载。","2"
"682","STREAM","流媒体技术",,"Stream泛指流媒体技术。流媒体实际指的是一种新的媒体传送方式，而非一种新的媒体，是指采用流式传输的方式在Internet播放的媒体格式。可指大河，也可指小河或小溪，指小溪时与brook,creek同义。creek侧重其狭长蜿蜒，缓缓流动，且多流入大河或湖泊。brook侧重发源于山泉。creek和stream都比brook大。stream还可引申表示事物连绵不断。","2"
"683","CBT","CBT",,,"2"
"684","EGP","外部网关协议",,"外部网关协议（Exterior Gateway Protocol）是AS之间使用的路由协议，最初于1982年由BBN技术公司的EricC.Rosen及DavidL.Mills提出。其最早在RFC827中描述，并于1984年在RFC904中被正式规范。EGP是一种简单的（网络）可达性协议，其与现代的距离-矢量协议和路径-矢量协议不同，它仅限适用于树状拓扑的网络。","2"
"685","IGP","内部网关协议 ",,"IGP（Interior Gateway Protocol,内部网关协议）是在一个自治网络内网关（主机和路由器）间交换路由信息的协议。路由信息能用于网间协议（IP）或者其它网络协议来说明路由传送是如何进行的。Internet网被分成多个域或多个自治系统。一个域（domain）是一组主机和使用相同路由选择协议的路由器集合，并由单一机构管理。IGP协议包括RIP、OSPF、IS-IS、IGRP、EIGRP。","2"
"686","BBN_RCC","BBN RCC 监视",,,"2"
"687","NVPII","网络语音协议",,,"2"
"688","PUP","PUP",,"PUP（隐匿LJ程序）是指那些尽管使用者可能同意下载，但其实并不想要的程序。PUP包括间谍软件，广告软件和拨号器，经常是在伴随用户下载需要的软件的时候一同被下载。由于那些行销公司拒绝别人将他们的产品称为“间谍软件”，因特网安全公司McAfee就创造了PUP这个术语来指代上述软件。","2"
"689","ARGUS","ARGUS",,,"2"
"690","EMCON","EMCON",,,"2"
"691","XNET","跨网调试器",,,"2"
"692","CHAOS","Chaos",,,"2"
"693","MUX","MUX协议",,,"2"
"694","DCNMEAS","DCN 测量子系统",,,"2"
"695","HMP","主机监督协议",,"主机监督协议/(Host Monitoring Protocol），需要考虑的事项应该是给定的以UDP或具体监控协议例如HMP为基础单重传覆盖协议.","2"
"696","PRM","数据包无线测量",,,"2"
"697","TRUNK1","第1主干",,"“trunk”在网络用语中一般译为：“主干线、中继线、长途线” ，不过一般不用译意，直接使用英文。在路由/交换网络中，trunk通常被称为“中继（透传）”。在语音级应用的线路中，trunk一般指“主干网络、电话干线”，即两个交换局或交换机之间的连接电路或信道，它为两端设备之间进行转接，作为信令和终端设备数据传输链路。","2"
"698","TRUNK2","第2主干",,"“trunk”在网络用语中一般译为：“主干线、中继线、长途线” ，不过一般不用译意，直接使用英文。在路由/交换网络中，trunk通常被称为“中继（透传）”。在语音级应用的线路中，trunk一般指“主干网络、电话干线”，即两个交换局或交换机之间的连接电路或信道，它为两端设备之间进行转接，作为信令和终端设备数据传输链路。","2"
"699","LEAF1","第 1 叶",,,"2"
"700","LEAF2","第 2 叶",,,"2"
"701","IRT","IRT协议",,,"2"
"702","BULK","BULK",,,"2"
"703","MFE_NSP","网络服务协议",,,"2"
"704","MERIT","节点间协议",,,"2"
"705","3PC","三阶段提交协议",,"三阶段提交（Three-phase commit），也叫三阶段提交协议（Three-phase commit protocol），是二阶段提交（2PC）的改进版本。","2"
"706","IDPR","域间策略路由协议",,"域间策略路由选择(InterdomainPolicyRouting，IDPR)是一个在域间实现源路由选择和基于策略的路由选择的链路状态路由选择协议。源路由选择由于分组本身保持路径信息而提供一些有用的增强特性。这对于初始发现路径是很必要的，但后继的分组只是简单地把路径放入自己的头部。　","2"
"707","DDP","数据报传输协议",,"数据报传输协议是存储转发方式的一种，可以使各个节点处于并行状态，大大缩短报文的传输时间。","2"
"708","CMTP","控制消息传输协议",,,"2"
"709","TPPP",,,,"2"
"710","IL","IL传输协议 ",,,"2"
"711","SDRP","源要求路由协议",,,"2"
"712","ROUTING","路由选择",,"在确定最佳路径的过程中，路由选择算法需要初始化和维护路由选择表（ routing table ）。路由选择表中包含的路由选择信息根据路由选择算法的不同而不同。一般在路由表中包括这样一些信息：目的网络地址，相关网络节点，对某条路径满意程度，预期路径信息等。","2"
"713","FRAGMENT","FRAGMENT",,,"2"
"714","IDRP","域内路由选择协议",,"域内路由选择协议（IDRP），用于为 OSI 网络环境提供路由选择服务，它类似 TCP/IP 网络的 BGP 协议。ISO 网络包含了终端系统、中间系统、区域（Area）和域（Domain）。终端系统指用户设备，中间系统指路由器。路由器形成的本地组称之为“区域”，多个区域组成一个“域”。IDRP 被设计来提供域内路由。IDRP 与 CLNP、IS-IS 和 ES-IS 协议相结合，为整个网络提供完整的路由选择。","2"
"715","DSR","DSR",,"DSR(data set ready)即：数据准备就绪，是RS232中的一握手信号。当调制/解调器启动时，在经过自身检测后，用DSR来声明已经准备就绪。因此它既是调制/解调器的输出，同时也是DTE（数据终端设备）的输入，该信号低电平有效。不管任何原因导致调制/解调器不能联通到电话，该信号都将保持无效状态以向DTE表明它不能接受和发送数据。","2"
"716","BNA","BNA",,,"2"
"717","INSLP",,,,"2"
"718","SWIPE","采用加密的 IP",,,"2"
"719","MOBILE","IP 移动性",,,"2"
"720","TLSP","传输层安全协议",,,"2"
"721","SKIP","SKIP",,,"2"
"722","NONE","NONE认证",,"none是一种登陆的认证方式，主要用于软件系统的登陆。像我们经常用到的交换机系统，数据库的系统都有涉及到none认证。","2"
"723","DSTOPTS",,,,"2"
"724","SHIM6_OL","SHIM6_OL",,"SHIM6，思想是修改终端网络协议栈 ,在 IP 路由子层之上 , IP 终端子层之下插入 Shim 层 ,使标识符( Identifier ) 和定位符(Locator)分离。","2"
"725","SATEXPAK","SATNET and Backroom EXPAK ",,,"2"
"726","KRYPTOLAN","Kryptolan",,,"2"
"727","RVD","RVD",,,"2"
"728","IPPC","Internet Pluribus Packet Core",,,"2"
"729","SATMON","SATNET监视",,,"2"
"730","VISA","VISA",,"VISA(Virtual Instrument Software Architecture，简称为""Visa"")，即虚拟仪器软件结构，是VXI plug&play联盟制定的I/O接口软件标准及其规范的总称。VISA提供用于仪器编程的标准I/O函数库，称为VISA库。VISA函数库驻留在计算机系统内，是计算机与仪器的标准软件通信接口，计算机通过它来控制仪器。","2"
"731","IPCV","数据包核心工具",,,"2"
"732","CPNX","计算机协议网络管理",,,"2"
"733","CPHB","计算机协议检测信号",,,"2"
"734","WSN","无线传感器网络",,"无线传感器网络(Wireless Sensor Networks, WSN)是一种分布式传感网络，它的末梢是可以感知和检查外部世界的传感器。WSN中的传感器通过无线方式通信，因此网络设置灵活，设备位置可以随时更改，还可以跟互联网进行有线或无线方式的连接。通过无线通信方式形成的一个多跳自组织网络。","2"
"735","PVP","定制向量处理器",,,"2"
"736","BRSATMON",,,,"2"
"737","SUNND","SUNND-PROTOCOL-Temporary",,,"2"
"738","WBMON","WIDEBAND监视",,,"2"
"739","WBEXPAK","WIDEBAND EXPAX",,,"2"
"740","ISOIP","ISO Internet协议",,,"2"
"741","VMTP","多用报文处理协议",,,"2"
"742","SVMTP","SVMTP",,,"2"
"743","TTP","TTP",,"TTP（Time-Triggered Protocol）总线由TTTech公司首先提出，并据此推出了基于TTP总线全开发流程的解决方案，现已广泛应用于欧美，成为替代目前军用总线（如429总线）的优选之一，同时欧美、日本的多家芯片厂商通过购买TTTech公司的IP核授权，也逐步实现了TTP总线的成熟商业化。","2"
"744","NSFNETIGP","NSFNETIGP",,,"2"
"745","DGP","异类网关协议",,,"2"
"746","TCF","TCF",,,"2"
"747","SPRITE","SPRITE",,,"2"
"748","LARP","轨迹地址解析协议",,"LARP就是Live Action Role Playing（实况角色扮演游戏）。简单的来说，就是把纸上角色扮演游戏那种嘴巴上讲讲的部份拿来真实生活中演出。是游戏与戏剧形式的交融。游戏与戏剧形式的交融。在LARP中，您要塑造一个角色，然后将他融入到虚拟世界中。根据游戏的不同，它有时如何一项体育运动，需要进行格斗，也有些时候可能只需进行大量交谈。但这种游戏的精华在于，玩家自己创建一个具有独特规则、主题，乃至意识形态的虚拟世界。您要塑造一个符合剧情的人物，并在游戏环境中扮演这个角色。这是一种全然置身其中的感觉，需要很多人接受一个虚构的现实。","2"
"749","MTP","媒体传输协议",,"是基于PTP(Picture Transfer Protocol)协议的扩展，主要用于传输媒体文件，其中有价值的应用就是同步DRM文件的license。","2"
"750","IPINIP","IP封装IP协议",,,"2"
"751","MICP","移动互联控制协议",,,"2"
"752","SCCSP","信号通讯安全协议",,,"2"
"753","ENCAP","封装标头",,,"2"
"754","GMTP","GMTP",,,"2"
"755","IFMP","Ipsilon 流量管理协议",,"IFMP用于相邻的IP交换机对某个数据流的信元进行重新标记。IFMP协议消息从IP交换机传递到其上游交换机。IFMP功能包括关联协议和重定向协议，前者发现链路上的对等IP交换机，后者管理指派给某个数据流的标记(VPI/VCI)。IFMP消息的发送端用IPv4的分组头封装IFMP信息，目的端IP地址是外部链路另一端的对等IP交换机。对等IP交换机的IP地址通过关联协议获得。","2"
"756","PNNI","PNNI",,"PNNI 是一种分层式的动态链路状态路由协议。它支持大规模的 ATM 网络。PNNI 协议为其信息使用 VPI/VCI 0.18。此外在多个网络情况下，PNNI 通过信令信息建立网络连接。PNNI 基于 UNI 4.0 和 Q.2931，UNI 4.0 中加入某些特定信息元素用于支持 PNNI 的路由处理。PNNI 信令包含了动态建立、维护和清除 ATM 连接的过程，该连接存在于2个 ATM 网络或2个 ATM 网络结点间的专用网到网络接口或网络结点接口上","2"
"757","ARIS","ARIS协议",,,"2"
"758","SCPS","空间通信协议",,"SCPS以地面网络普遍应用的TCP/IP四层分层结构为模型，在局部兼容interact基础上，为适应空间网络特性而进行了适当的剪裁和扩充。在空间通信网络中广泛应用。","2"
"759","QNX","QNX",,"Gordon Bell和Dan Dodge在1980年成立了Quantum Software Systems公司，他们根据大学时代的一些设想写出了一个能在IBM PC上运行的名叫QUNIX（Quick UNIX）的系统，直到AT&T发律师函过来才把名字改成QNX。","2"
"760","AN","AN",,"AN (Access Network)电信部门业务节点与用户终端设备之间的实施系统。它可以部分或全部代替传统的用户本地线路网，并可包括复用、交叉连接和传输功能。","2"
"761","SNP","Sitara网络协议",,"SNP(Sitara Networks Protocol)协议是GE的专用协议，它使用计算机的RS232串行口与GE90系列PLC通讯。","2"
"762","COMPAQ","COMPAQ",,,"2"
"763","DDX","D-II数据交换",,"大单动向基于Level-2的逐单分析功能，是一个短中线兼顾的技术指标。","2"
"764","IATP","交互式代理传输协议",,,"2"
"765","SRP","无线协议",,"SCSI远程协议SRP(SCSI RDMA protocol)，是IB SAN的一种协议 ，也被称为SRP(SCSI Remote Protocol)。其主要作用是把SCSI协议的命令和数据通过RDMA的方式跑到例如Infiniband这种网络上，和iSCSI协议类似，也需要一个target端和 initiator端。","2"
"766","UTI","UTI",,,"2"
"767","SMP","简单邮件协议",,"短信协议（Short Message Protocol，SMP）指的是手机所支持的短信息协议，也称为多媒体协议。目前主要有SMS短信、EMS短信和MMS彩信三种。","2"
"768","SM","SM",,,"2"
"769","FIRE","路由器",,,"2"
"770","CRTP","无线传输协议",,"CRTP技术是报文压缩的一种技术，通过将普通IP+UDP+RTP报文压缩成小字节报文，可以极大的降低使用带宽。本文档介绍了该项技术的主要原理。","2"
"771","CRUDP","无线用户数据报",,,"2"
"772","SSCOPMCE","SSCOPMCE",,"业务特定面向连接的协议在多链路和无连接的环境","2"
"773","IPLT","IPLT协议",,,"2"
"774","SPS","安全数据包防护",,"SPS包含有或引用了可执行软件、源文件以及软件支持的信息。包括一个计算机软件配置项(CSCI)“已建成”的设计信息和编辑、构造及修改的过程等。","2"
"775","PIPE","Pipe协议",,,"2"
"776","MANET","移动自组织网络",,"MANET在计算机技术方面定义为移动自组网络的工作组，全称为Mobile Ad-hoc NETworks。","2"
"777","SHIM6","SHIM6",,"SHIM6，思想是修改终端网络协议栈 ,在 IP 路由子层之上 , IP 终端子层之下插入 Shim 层 ,使标识符( Identifier ) 和定位符(Locator)分离。","2"
"778","WESP","WESP协议",,,"2"
"779","STP","生成树协议",,"STP（Spanning Tree Protocol）是生成树协议的英文缩写。该协议可应用于在网络中建立树形拓扑，消除网络中的环路，并且可以通过一定的方法实现路径冗余，但不是一定可以实现路径冗余。生成树协议适合所有厂商的网络设备，在配置上和体现功能强度上有所差别，但是在原理和应用效果是一致的。","2"
"780","VJCOMPRESS","vj压缩",,"VJ压缩算法是一种适用于串行链路的TCP/IP头压缩算法，对小包有较高的压缩率，可以有效提高串行链路的链路利用率，并有效提高交互式应用的响应速度。compress，是一个相当古老的 unix 档案压缩指令，压缩后的档案会加上一个 .Z 延伸档名以区别未压缩的档案，压缩后的档案可以以 uncompress 解压。","2"
"781","VJ_Uncompress","vj解压",,"VJ压缩算法是一种适用于串行链路的TCP/IP头压缩算法，对小包有较高的压缩率，可以有效提高串行链路的链路利用率，并有效提高交互式应用的响应速度","2"
"782","NBNS","NBNS",,"网络基本输入/输出系统 (NetBIOS) 名称服务器 (NBNS) 协议是 TCP/IP 上的 NetBIOS (NetBT) 协议族的一部分，它在基于 NetBIOS 名称访问的网络上提供主机名和地址映射方法","2"
"783","NBDGM","网络数据报输入输出协议",,"NBDGM是Windows的网络数据报输入输出协议","2"
"784","NBSS","NetBIOS 会话服务",,"NetBIOS Session Service用于Windows""文件和打印机共享""和SAMBA","2"
"785","Enhanced_GRE",,,,"2"
"786","SSL_2","SSL2",,"SSL(Secure Sockets Layer 安全套接层),及其继任者传输层安全（Transport Layer Security，TLS）是为网络通信提供安全及数据完整性的一种安全协议。TLS与SSL在传输层对网络连接进行加密。","2"
"790","SSH_AH",,,,"2"
"791","SSH_ESP",,,,"2"
"792","HTTP_OPTIONS",,,"OPTIONS 请求查询服务器的性能，或者查询与资源相关的选项和需求","2"
"793","HTTP_HEAD",,,"请求获取由Request-URI所标识的资源的响应消息报头","2"
"794","HTTP_GET",,,"请求获取Request-URI所标识的资源","2"
"795","HTTP_POST",,,"在Request-URI所标识的资源后附加新的数据","2"
"796","HTTP_PUT",,,"请求服务器存储一个资源，并用Request-URI作为其标识","2"
"797","HTTP_DELETE",,,"请求服务器删除Request-URI所标识的资源","2"
"798","HTTP_TRACE",,,"请求服务器回送收到的请求信息，主要用于测试或诊断","2"
"799","HTTP_CONNECT",,,"CONNECT 保留将来使用","2"
"800","HTTP_PATCH",,,"PATCH方法是新引入的，是对PUT方法的补充，用来对已知资源进行局部更新","2"
"801","VOIP_RTCP",,,,"2"
"802","LLMNR","链路本地多播名称解析",,"链路本地多播名称解析（LLMNR）是一个基于协议的域名系统（DNS）数据包的格式，使得双方的IPv4和IPv6的主机来执行名称解析为同一本地链路上的主机。它是包含在Windows Vista中，Windows Server 2008中，Windows 7中，Windows 8中和的Windows 10。它也被实施systemd在Linux上-resolved。 LLMNR定义在RFC 4795。","2"
"803","Tencent_MayBe_OICQ","腾讯QQ",,"QQ是腾讯QQ的简称，是腾讯公司开发的一款基于Internet的即时通信（IM）软件。目前QQ已经覆盖Microsoft Windows、OS X、Android、iOS、Windows Phone等多种主流平台。其标志是一只戴着红色围巾的小企鹅。","2"
"805","Tencent_QQMail","QQ邮箱","网络邮箱","QQ邮箱是腾讯公司2002年推出，向用户提供安全、稳定、快速、便捷电子邮件服务的邮箱产品，已为超过1亿的邮箱用户提供免费和增值邮箱服务。QQ邮件服务以高速电信骨干网为强大后盾，独有独立的境外邮件出口链路，免受境内外网络瓶颈影响，全球传信。采用高容错性的内部服务器架构，确保任何故障都不影响用户的使用，随时随地稳定登录邮箱，收发邮件通畅无阻。","2"
"806","SSDP","简单服务发现协议",,"SSDP，即简单服务发现协议（SSDP，Simple Service Discovery Protocol），是一种应用层协议，是构成通用即插即用(UPnP)技术的核心协议之一。","2"
"807","GTP_MANAGER","GTP_MANAGER",,"GTP是一组基于IP的高层协议，位于TCP/IP或UDP/IP等协议上，主要用于在GSM和UMTS和LTE网络中支持通用分组无线服务(GPRS)的通讯协议。","2"
"810","IPMessage","飞鸽传书",,"IP Messenger是一款局域网内部聊天、文件传输工具，具有很多优点，如数据通讯不需要建立服务器、直接在两台电脑间通信和数据传输，支持文件及文件目录的传输，安全快捷以及小巧方便等优异特点，因此很多公司都采用它作为部门、公司内部的IM即时通信工具。","2"
"811","Teredo","teredo",,"Teredo （又称为面向 IPv6 的 IPv4 NAT 网络地址转换穿越)，是一项 IPv6 / IPv4 过渡技术，在 IPv6 / IPv4 主机位于一个或多个 IPv4 NAT 之后时，用来为单播 IPv6 连接提供地址分配和主机间自动隧道。为能够通过 IPv4 NAT， IPv6 数据包作为基于 IPv4 的用户数据包协议(UDP) 消息发送出去。详细解释了 Teredo 客户端的发起通信的方式、特定于 Teredo 主机的中继、仅支持 IPv6 主机使用 IPv4 Internet 的方法、 IPv6 Internet、 Teredo 服务器以及 Teredo 中继。","2"
"812","ADWIN","Adwing",,"Adwing是基于盘石强大的技术实力研发的一款行业内功能最强大的可视化互联网广告多维度监控和分析软件，具备流量分析、访客分析、网页分析、关键词分析和效果转化分析等强大功能，可自动优化调整广告效果，以达到最大性价比广告展现与点击，并进行互联网广告效果精准评估，做出科学的推广决策报告。","2"
"813","AGENTX","AgentX",,"扩展代理（AgentX）也叫做扩展 SNMP 代理，为扩展 SNMP 代理定义的一个协议。这个结果技术规范将允许独立发展子代理人来与运行在网络中的一个总代理进行通话","2"
"814","AIM","aim",,"AIM的全称是：AmericanOnline(AOL) Instant Messenger美国在线即时通讯软件，AIM是美国在线（AOL）于1997年5月推出的即时通讯软件，类似于MSN，Yahoo！","2"
"815","AJP13","ajp13",,"AJP13是定向包协议。因为性能原因，使用二进制格式来传输可读性文本。","2"
"816","ALC","ALC协议",,,"2"
"817","ALCAP","接入链路控制应用协议",,"ALCAP（Access Link Control Application Protocol，接入链路控制应用协议），又称为Q.AAL2协议，遵循ITU-T Q.2630.1规范，定义了与用户面建立、释放传输承载的方式。处于Iub/Iur/Iu-CS传输网络层的控制面，信令承载类型为SAAL UNI和MTP3-B。ALCAP内部由两个层面组成，Q.AAL2协议处理层以及两个STC适配层。Q.AAL2协议处理层完成所有的协议功能，STC完成原语适配以屏蔽底层差异（SAAL、MTP3-B）。ALCAP的基本功能是在两个信令点之间建立、释放AAL2连接。同时，对该信令系统内微信元的通道、通路等资源进行必要的维护和管理。","2"
"818","AMQP","高级消息队列协议",,"AMQP，即Advanced Message Queuing Protocol,一个提供统一消息服务的应用层标准高级消息队列协议,是应用层协议的一个开放标准,为面向消息的中间件设计。基于此协议的客户端与消息中间件可传递消息，并不受客户端/中间件不同产品，不同的开发语言等条件的限制。Erlang中的实现有 RabbitMQ等。","2"
"819","AMR","自主移动机器人",,"AMR（Adaptive Multi-Rate和Adaptive Multi-Rate Wideband ）接口卡将音频和MODEM的接口电路、模拟电路和解码器制作在上面。 AMR狙击枪/AMR-2 由中国兵器装备集团公司四川华庆机械有限责任公司生产","2"
"820","ANCP","ANCP协议",,"ANCP（Access Node Control Protocol）协议提供了BRAS设备和接入节点（AN：Access Node，如DSLAM设备）之间控制信息传递的通道。","2"
"821","ANS","ANSI",,"（Automatic Noise Suppression），ANS可探测出背景固定频率的杂音并消除背景噪音，例如：风扇、空调声自动滤除。呈现出与会者清晰的声音。","2"
"822","ANSI","ANSI",,"EDI文档标准ANSI（ANSI ASC X12） 是为了满足商务文档之间的电子数据交换也就是EDI通讯而由美国国家标准委员会在1979年创立的认可标准委员会（ASC）X12制定的。","2"
"900","NETMAN","网络人",,"网络人(Netman)单、安全的远程控制软件。支持远程开机、关机,监控对方电脑屏幕，遥控鼠标键盘,监控摄像头（可将监控内容录象）,远程管理文件,并可以穿透局域网。","2"
"901","NETMAN_NEGOTIATION",,,,"2"
"902","XT800","协通XT800",,"协通XT800是协之通公司出品的一款远程控制软件，是原快递通KDT全面升级的新系列，包括个人版、企业版和远程助手版，是一系列可穿透防火墙的远程控制软件，功能强大，操作简单。快递通（简称KDT）国内最早最受企业欢迎的远程控制软件。","2"
"903","RADMIN","Radmin",,"Radmin (Remote Administrator)是一款屡获殊荣的远程控制软件，它将远程控制、外包服务组件、以及网络监控结合到一个系统里，提供目前为止最快速、强健而安全的工具包。","2"
"904","SUNLOGIN","向日葵远程控制软件",,"向日葵远程控制是一款面向企业和专业人员的远程PC管理和控制的服务软件。您在任何可连入互联网的地点，都可以轻松访问和控制安装了向日葵远程控制客户端的远程主机，整个过程完全可以通过浏览器进行，无需再安装软件。","2"
"1021","UNK_dport_443",,,,"2"
"1022","UNK_sprot_443",,,,"2"
"1023","UNK_dport_80",,,,"2"
"1024","UNK_sprot_80",,,"2","2"
"1051","FTP_Control","FTP Control",,"FTP Control是一款FTP客户端程序，可以搜寻远端Server中的文件目录，自动拨号上网，书签管理，支持Proxy设定，自动定时上下传文件，有续传功能。虽是共享软件，但试用期过后若未注册则自动成为免费的Lite版本，不会完全将功能锁住。","2"
"1062","MDNS","组播DNS",,"mDNS即组播DNS（multicast DNS）。使用5353端口，在内网没有DNS服务器时，就会出现此组播信息。","2"
"1073","POSTGRES","PostgreSQL",,"PostgreSQL是以加州大学伯克利分校计算机系开发的 POSTGRES，现在已经更名为PostgreSQL，版本 4.2为基础的对象关系型数据库管理系统（ORDBMS）。PostgreSQL支持大部分 SQL标准并且提供了许多其他现代特性：复杂查询、外键、触发器、视图、事务完整性、MVCC。同样，PostgreSQL 可以用许多方法扩展，比如， 通过增加新的数据类型、函数、操作符、聚集函数、索引。免费使用、修改、和分发 PostgreSQL，不管是私用、商用、还是学术研究使用。","2"
"1077","APPLEJUICE","Applejuic",,"Applejuic  集中和初始Edonkei对等网络文件共享网络相似搜索和查找文件。 Applejuic客户端用户和 Applejuic 服务器的连接.直接从另一个客户或“点”下载","2"
"1078","DIRECTCONNECT","Direct Connect",,"Direct Connect是一款P2P客户端软件，运行环境支持WinNT Win9X Win2000。","2"
"1079","SOCRATES",,,,"2"
"1080","WINMX","WinMX",,"是点对点(Peer to Peer)档案分享软件，由Frontcode Technologies开发。虽然Frontcode由WinMX 2开始，制作了一个拥有权协议，称为WinMX Peer Network Protocol (WPNP)。WPNP 2在WinMX 3.0淘汰掉，而WPNP 3协议取而代之成立。Lopster曾有WPNP 2技援，可是在WPNP 3中被锁了。","2"
"1081","VMWARE","VMware",,"VMware（中文名威睿”，纽约证券交易所“代码：VMW） 虚拟机软件，是全球桌面到数据中心虚拟化解决方案的领导厂商。全球不同规模的客户依靠VMware来降低成本和运营费用、确保业务持续性、加强安全性并走向绿色。2008年，VMware年收入达到19亿美元，拥有逾150,000的用户和接近22,000多家合作伙伴，是增长最快的上市软件公司之一。VMware总部设在加利福尼亚州的帕罗奥多市（Palo Alto）","2"
"1082","FILETOPIA","Filetopia",,"为用户提供了一种安全的、个性化的、有趣的交换信息的方式，你可以拥有你自己的文件服务器，可以找寻其他的聊天对象，互相发送信息和共享文件，而且一切都是安全和保密的","2"
"1083","IMESH","imesh",,"iMesh是一个文件分享软件，能够让你设定分享文件的类型，音乐、影片或其他文件；也能够让你搜寻并且下载你想要的文件。","2"
"1084","KONTIKI",,,,"2"
"1091","XBOX","Xbox",,"Xbox，是由美国微软公司开发并于2001年发售的一款家用电视游戏机。","2"
"1096","PPLIVE","PPTV网络电视：别名PPLive",,"PPTV网络电视：别名PPLive，是由上海聚力传媒技术有限公司开发运营在线视频软件，它是全球华人领先的、规模最大、拥有巨大影响力的视频媒体，全面聚合和精编影视、体育、娱乐、资讯等各种热点视频内容，并以视频直播和专业制作为特色，基于互联网视频云平台PPCLOUD通过包括PC网页端和客户端，手机和PAD移动终端，以及与牌照方合作的互联网电视和机顶盒等多终端向用户提供新鲜，及时，高清和互动的网络电视媒体服务。","2"
"1097","PPSTREAM","PPStream",,"pps（全称PPStream）是全球第一家集P2P直播点播于一身的网络电视软件，能够在线收看电影、电视剧、体育直播、游戏竞技、动漫、综艺、新闻、财经资讯等。pps网络电视完全免费，无需注册，下载即可使用；播放流畅，P2P传输，越多人看越流畅，完全免费，但是广告时间很长，是广受网友推崇和吐槽的上网装机软件。 PPS获得了2012年TVB所有电视剧集、综艺节目的独家版权。2012年11月12日，艾瑞ivideotracker最新报告显示，2012年8月，PPS网络电视的覆盖人数突破1亿，占行业整体的1/3。2013年3月6日 PPS设爱频道事业部，高清和移动是2013战略重点。2013年5月7日，百度宣布3.7亿美元收购PPS视频业务。2013年10月16日，爱奇艺公司宣布爱奇艺与PPS整合的基础工作完成，两大品牌全面升级，并发布了升级后新品牌LOGO，从“在一起，更好看”转变为“开启全新视界”，同时两个子品牌也有了更明确的分工。","2"
"1098","ZATTOO","Zattoo",,"Zattoo是一款简单实用的程序。它为您提供性能良好的免费电视，让您在互联网上享受Zattoo电视的乐趣。","2"
"1099","SHOUTCAST","SHOUTcast WIN32 server",,"Shoutcast是网络即时播放mp3节目的一个网站,它也是着名的mp3播放软件Winamp公司Nullsoft的产品之一.Shoutcast可以在Windows和Unix两种操作平台下播放mp3串流(streaming)音乐.Winamp是听mp3串流音乐的客户端软件,而如果要让你的电脑成为可以播放mp3串流音乐的服务器,那就需要SHOUTcast server啦!SHOUTcast server并不一定要安装在NT服务器或Unix服务器上,它也可以直接安装在Windows 95或98上,而且可以和Winamp一起执行.SHOUTcast server和一般的服务器是不同的,它可以分一服务器播送mp3串流文件的负担,而且会自动和SHOUTcast的目录(http:yp.shoutcast.com)整合,以便让你的SHOUTcast server可以被世界上最多的人同时听到.","2"
"1100","SOPCAST","SopCast ","P2P","SopCast是一个基于P2P的广播系统。简单，易用的系统，可以在Internet互联网上观看流媒体的节目。与其它P2P流媒体直播系统相比，SopCast具有业界最小的延时，这个特性使得SopCast比其它凭P2P流媒体系统更适合做实时直播。","2"
"1101","TVANTS","tvants",,"TVants是一种全新的流媒体播放软件，它的核心技术类似于现在非常流行的P2P(PEER TO PEER)， 即一个播放节点同时和数个播放节点交换(提供或索取)数据。","2"
"1102","TVUPLAYER","TVUplayer",,"TVUPlayer， 是 TVU networks 公司的一款网络电视播放软件，旨在使每个广播者都可以把他们的播放内容通过无限频道承载量、低成本的网络电视平台传送到世界各地，使广播者更好地实现商业价值。","2"
"1106","SOULSEEK","SoulSeek",,"SoulSeek是一款应用软件，适用于pc平台。","2"
"1111","MSN","MSN Messenger",,"MSN，全称Microsoft Service Network，是微软公司（Microsoft）旗下的门户网站。","2"
"1112","OSCAR","OSCAR协议",,,"2"
"1114","NDPI_BATTLEFIELD","Battlefield协议",,,"2"
"1117","STEAM","Steam ",,"Steam平台是Valve公司聘请BitTorrent(BT下载)发明者布拉姆·科恩亲自开发设计的游戏平台。Steam平台是目前全球最大的综合性数字发行平台之一。玩家可以在该平台购买、下载、讨论、上传和分享游戏和软件。","2"
"1118","HALFLIFE2_AND_MODS",,,,"2"
"1133","PCANYWHERE","PcAnywhere ",,"PcAnywhere是一款远程控制软件，你可以将你的电脑当成主控端去控制远方另一台同样安装有pcANYWHERE的电脑（被控端），你可以使用被控端电脑上的程序或在主控端与被控端之间互传文件。你也可以使用其闸道功能让多台电脑共享一台MODEM或是向网路使用者提供打进或打出的功能。","2"
"1136","USENET","新闻组 ",,"新闻组(英文名Usenet或NewsGroup)，简单地说就是一个基于网络的计算机组合，这些计算机被称为新闻服务器，不同的用户通过一些软件可连接到新闻服务器上，阅读其他人的消息并可以参与讨论。新闻组是一个完全交互式的超级电子论坛，是任何一个网络用户都能进行相互交流的工具。","2"
"1138","IAX","Inter-Asterisk eXchange",,"IAX 是 Inter-Asterisk eXchange 的缩写。也就是 Asterisk 内部交换协议。模拟 电话仍得到广泛使用的原因之一是用户可以买到廉价的电话机，然后将它接上后，就可以立即开始打电话。而你在使用新IP电话机时是否有过这样的经历？大多数VOIP协议在某些环境下难于配置，因而使许多新VoIP产品很难做到开箱即用。 易用性不够可能成为阻碍VoIP市场发展的巨大障碍。为此，IAX（Inter-Asterisk Exchange）新协议产生了。这种新协议的目标是最大限度减少信令和媒体所需 带宽，为NAT提供内部支持，同时保持对未来增强功能的可扩展性。无需额外的配置就可让IAX穿越NAT 防火墙。","2"
"1140","AFP","AFP ",,"AFP协议主要用与Apple之间的通信，Appletalk的一种。","2"
"1141","STEALTHNET","StealthNet",,"StealthNet是一个匿名的点对点技术文件共享软件，以RShare用户端为基础，并且进行强化。","2"
"1147","NDPI_ARMAGETRON","Armagetron协议",,,"2"
"1149","DOFUS","DOFUS",,"DOFUS是法国AnkamaStudio游戏公司开发的角色扮演、 卡通题材的网络游戏。《DOFUS》是一部将英雄奇幻和色彩鲜明的2D设计结合起来的MMORPG，由Ankama Studio出品。它不仅是角色扮演游戏，同时也是互动卡通，意在引游戏高手和休闲玩家。","2"
"1150","FIESTA",,,,"2"
"1152","GUILDWARS","Guild Wars",,"激战是3D大型多人在线角色扮演游戏。《激战》是Patrick Wyatt、Mike O'Brien、Jeff Strain，三名曾经的暴雪核心员工离开暴雪，独自成立了一个名为Triforge现改名为ArenaNet的游戏开发公司所设计的。","2"
"1153","HTTP_ACTIVESYNC","HTTP_ACTIVESYNC",,"用于在消息服务器和移动设备之间同步电子邮件、策略和其他内容的协议。","2"
"1156","MAPLESTORY","冒险岛Online ",,"《冒险岛Online》是由韩国WIZET和NEXON制作开发的一款2D横版卷轴网络游戏，于2004年7月24日在中国大陆正式上线，由盛大网络负责运营。","2"
"1157","MSSQL","msSQL ",,"ms SQL是指微软的SQLServer数据库服务器，它是一个数据库平台，提供数据库的从服务器到终端的完整的解决方案，其中数据库服务器部分，是一个数据库管理系统，用于建立、使用和维护数据库。SQL Server一开始并不是微软自己研发的产品，而是当时为了要和IBM竞争时，与Sybase合作所产生的，其最早的发展者是Sybase，同时微软也和Sybase合作过 SQL Server 4.2版本的研发，微软亦将SQL Server 4.2移植到Windows NT（当时为3.1版），在与Sybase终止合作关系后，自力开发出SQL Server 6.0版，往后的SQL Server即均由微软自行研发。","2"
"1163","SKYPE","Skype",,"Skype是一款即时通讯软件，其具备IM所需的功能，比如视频聊天、多人语音会议、多人聊天、传送文件、文字聊天等功能。它可以高清晰与其他用户语音对话，也可以拨打国内国际电话，无论固定电话、手机、小灵通均可直接拨打，并且可以实现呼叫转移、短信发送等功能。","2"
"1164","DCERPC","远程过程调用",,"RPC是远程过程调用（Remote Procedure Call）的缩写形式。SAP系统RPC调用的原理其实很简单，有一些类似于三层构架的C/S系统，第三方的客户程序通过接口调用SAP内部的标准或自定义函数，获得函数返回的数据进行处理后显示或打印。","2"
"1169","NDPI_CITRIX","Citrix协议",,,"2"
"1177","TEAMVIEWER","TeamViewer",,"TeamViewer是一个能在任何防火墙和NAT代理的后台用于远程控制的应用程序，桌面共享和文件传输的简单且快速的解决方案。为了连接到另一台计算机，只需要在两台计算机上同时运行 TeamViewer 即可，而不需要进行安装（也可以选择安装，安装后可以设置开机运行）。该软件第一次启动在两台计算机上自动生成伙伴 ID。只需要输入你的伙伴的ID到TeamViewer，然后就会立即建立起连接。","2"
"1178","LOTUS_NOTES","lotus notes ",,"Lotus Notes/Domino 是一个世界领先的企业级通讯、协同工作及Internet/Intranet平台；具有完善的工作流控制、数据库复制技术和完善可靠的安全机制；尤其适合于处理各种非结构化与半结构化的文档数据、建立工作流应用、建立各类基于Web的应用。它全面实现了对非结构化信息的管理和共享，内含强大的电子邮件功能及工作流软件开发环境，是实现群组协同工作、办公自动化的最佳开发环境。","2"
"1184","SPOTIFY","Spotify",,"potify（声田）[1]  是全球最大的正版流媒体音乐服务平台，2008年10月在瑞典首都斯德哥尔摩正式上线。Spotify提供免费和付费两种服务，免费用户在使用Spotify的服务时将被插播一定的广告，付费用户则没有广告，且拥有更好的音质","2"
"1185","H323","H323",,"在传统电话系统中，一次通话从建立系统连接到拆除连接都需要一定的信令来配合完成。同样，在IP电话中，如何寻找被叫方、如何建立应答、如何按照彼此的数据处理能力发送数据，也需要相应的信令系统，一般称为协议。目前在国际上，比较有影响的IP电话方面的协议包括ITU-T提出的H.323协议和IETF提出的SIP协议","2"
"1188","CISCOVPN","虚拟专用网络 ",,"虚拟专用网络的功能是：在公用网络上建立专用网络，进行加密通讯。在企业网络中有广泛应用。VPN网关通过对数据包的加密和数据包目标地址的转换实现远程访问。VPN有多种分类方式，主要是按协议进行分类。VPN可通过服务器、硬件、软件等多种方式实现。","2"
"1189","TEAMSPEAK","Teamspeak",,"Teamspeak（简称TS）是一款团队语音通讯工具，但比一般的通讯工具具有更多的功能而且使用方便。它由服务器端程序和客户端程序两部分组成，如果不是想自己架设TS服务器，只需下载客户端程序即可。Teamspeak依靠先进的体系结构，方便灵活的应用功能，特别是领先的多媒体技术，为用户提供了一款强大的网络通讯工具。","2"
"1190","TOR","洋葱头",,"（The Onion Router）是一种软件，是第二代洋葱路由（onion routing）的一种实现，用户通过它可以在因特网上进行匿名交流。","2"
"1195","CORBA","公共对象请求代理体系结构",,"CORBA（Common Object Request Broker Architecture,公共对象请求代理体系结构，通用对象请求代理体系结构）是由OMG组织制订的一种标准的面向对象应用程 序体系规范。或者说 CORBA体系结构是对象管理组织（OMG）为解决分布式处理环境(DCE)中，硬件和软件系统的互连而提出的一种解决方案；OMG组织是一个国际性的非盈利组织，其职责是为应用开发提供一个公共框架，制订工业指南和对象管理规范，加快对象技术的发展。","2"
"1199","SOCKS5","SOCKS5代理",,"采用socks协议的代理服务器就是SOCKS服务器，是一种通用的代理服务器。Socks是个电路级的底层网关，是DavidKoblas在1990年开发的，此后就一直作为Internet RFC标准的开放标准。Socks 不要求应用程序遵循特定的操作系统平台，Socks 代理与应用层代理、 HTTP 层代理不同，Socks 代理只是简单地传递数据包，而不必关心是何种应用协议（比如FTP、HTTP和NNTP请求）。所以，Socks代理比其他应用层代理要快得多","2"
"1200","SOCKS4","SOCKS4代理",,"采用socks协议的代理服务器就是SOCKS服务器，是一种通用的代理服务器。Socks是个电路级的底层网关，是DavidKoblas在1990年开发的，此后就一直作为Internet RFC标准的开放标准。Socks 不要求应用程序遵循特定的操作系统平台，Socks 代理与应用层代理、 HTTP 层代理不同，Socks 代理只是简单地传递数据包，而不必关心是何种应用协议（比如FTP、HTTP和NNTP请求）。所以，Socks代理比其他应用层代理要快得多","2"
"1201","RTMP","实时消息传输协议",,"RTMP是Real Time Messaging Protocol（实时消息传输协议）的首字母缩写。该协议基于TCP，是一个协议族，包括RTMP基本协议及RTMPT/RTMPS/RTMPE等多种变种。RTMP是一种设计用来进行实时数据通信的网络协议，主要用来在Flash/AIR平台和支持RTMP协议的流媒体/交互服务器之间进行音视频和数据通信。支持该协议的软件包括Adobe Media Server/Ultrant Media Server/red5等","2"
"1202","PANDO","Pando ",,"Pando是一款通过Email发送的PtP软件,最大发送附件1G,而收件者收到的只是一个Pando种子，支持Win2003 Win9X Vista Win2000 WinXP。","2"
"1203","VIBER","Viber",,"Viber是一种智能手机用的跨平台网络电话及即时通讯软件，能在3G和WiFi网络上运作。Viber最先推出的是在iPhone上运行版本。2011年5月，开发商Viber Media在谷歌电子市场上发布了该应用的Android测试版。同年7月20日，官方推出了Android的正式版本","2"
"1206","ZMQ","zmq协议",,,"2"
"1207","MMS","会员管理系统",,"MMS是英文缩写，它可以是Membership Management System的缩写，中文译名为会员管理系统。也可以是Multimedia Messaging Service的缩写，中文译为彩信。","2"
"1208","POPO","网易泡泡",,"网易泡泡是由网易公司开发的一款免费的绿色多媒体即时通讯工具，POPO 不仅支持即时文字聊天、语音通话、视频对话、文件断点续传等基本即时通讯功能，还提供邮件提醒、多人兴趣组、在线及本地音乐播放、网络电台、发送网络多媒体文件、网络文件共享、自定义软件皮肤等多种功能，并可与移动通讯终端等多种通讯方式相连。还有popo游戏大厅，当中有多款游戏，供网友选择。","2"
"1209","WHOISDAS","WHOISDAS协议",,,"2"
"1300","LoopBack","loopback",,"loopback指本地环回接口（或地址），亦称回送地址()。此类接口是应用最为广泛的一种虚接口，几乎在每台路由器上都会使用。","2"
"1301","Bootstrap_Protocol","引导程序协议",,"BOOTP（Bootstrap Protocol，引导程序协议）是一种引导协议，基于IP/UDP协议，也称自举协议，是DHCP协议的前身。BOOTP用于无盘工作站的局域网中，可以让无盘工作站从一个中心服务器上获得IP地址。通过BOOTP协议可以为局域网中的无盘工作站分配动态IP地址，这样就不需要管理员去为每个用户去设置静态IP地址。","2"
"1302","FCIP","FCIP",,"FCIP（Entire Fibre Channel Frame Over IP）基于IP的光纤通道（FCIP）是连接TCP/IP网络链路上的光纤通道架构的一项IETF建议标准。该协议可以作为通过密集波分多路复用和按光纤来连接存储区域网的一项替代选择。开发使用能够承担得起并且可以随时使用的IP服务，可以大大降低每个月的广域网连接成本并扩大光纤通道站点之间的最大距离。","2"
"1303","WakeOnLan","远程唤醒协议",,"一般将其称为“网络唤醒”、“远端唤醒”技术。WOL是一种技术，同时也是该技术的规范标准，它的功效在于让已经进入休眠状态或关机状态的电脑，透过局域网（多半为以太网）的另一端对其发令，使其从休眠状态唤醒、恢复成运作状态，或从关机状态转成开机状态。此外，与WOL相关的技术也包括远端下令关机、远端下令重新开机等相关的遥控机制。","2"
"1400","RDPv8","RDP协议",,,"2"
"1451","Il_DNS","Il_DNS协议",,,"2"
"1500","Simatic_S7","工控协议-西门子S7",,,"2"
"10000","UNKNOWN","未知应用","未知应用",,"1"
"10001","APP_FTP","文件传输协议","文件共享",,"1"
"10002","APP_NNTP","新闻阅读器","基础协议",,"1"
"10022","APP_WakeOnLan","远端唤醒","基础协议",,"1"
"10026","APP_RIP","路由协议-RIP","路由协议","RFC 2453","1"
"10027","APP_IGRP","内部网关路由协议","路由协议","CISCO专有","1"
"10028","APP_EIGRP","增强内部网关路由协议","路由协议","CISCO专有","1"
"10029","APP_OSPF","内部网关协议","路由协议","RFC 2328RFC 5340","1"
"10030","APP_IGP",,,,"1"
"10031","APP_EGP","外部网关协议","路由协议","RFC827","1"
"10032","APP_BGP","边界网关协议","路由协议",,"1"
"10033","APP_VRRP","虚拟路由冗余协议","路由协议",,"1"
"10034","APP_SNMP","简单网络管理协议","网管协议","RFC 2578","1"
"10035","APP_DHCP","动态主机配置协议","网管协议","RFC 2131","1"
"10037","APP_DHCPv6","动态主机配置协议-IPV6","网管协议",,"1"
"10040","APP_ICMP_v4","互联网控制报文协议","网管协议",,"1"
"10042","APP_ICMP_v6","互联网控制报文协议v6","基础协议",,"1"
"10044","APP_ARP","地址解析协议","基础协议",,"1"
"10046","APP_NBNS","NetBIOS 名称服务器","基础协议","微软","1"
"10049","APP_NBDGM","Windows的网络数据报输入输出协议","基础协议","微软","1"
"10056","APP_NBSS","NetBIOS 会话服务","基础协议","微软","1"
"10061","APP_SSH","SSH远程控制","远程控制",,"1"
"10066","APP_Telnet","telnet远程控制","远程登陆",,"1"
"10067","APP_MAIL_SMTP","简单邮件传输协议","邮件协议",,"1"
"10069","APP_WHOISDAS",,,,"1"
"10070","APP_Tacacs+","终端访问控制器访问控制系统","认证协议",,"1"
"10071","APP_DNS","域名访问协议","域名协议",,"1"
"10074","APP_Il_DNS",,,,"1"
"10093","APP_KERBEROS","Kerberos认证","认证协议",,"1"
"10101","APP_MAIL_POP","邮局协议版本3","邮件协议",,"1"
"10103","APP_DCERPC","远程过程调用","远程控制",,"1"
"10105","APP_MAIL_IMAP","邮件访问协议","邮件协议",,"1"
"10113","APP_LDAP","轻量目录访问协议","文件共享",,"1"
"10120","APP_CISCOVPN","思科VPN协议","VPN协议","CISCO专有","1"
"10123","APP_SMB","网络文件共享协议","网络基础协议","微软","1"
"10127","APP_SYSLOG","系统日志协议","基础协议",,"1"
"10136","APP_RTSP","实时流传输协议","音视频协议",,"1"
"10138","APP_VMWARE","Vmware虚拟机","远程控制",,"1"
"10145","APP_SOCKS5","网络代理-Socks","匿名网络",,"1"
"10146","APP_OPENVPN","OpenVPN协议","VPN协议",,"1"
"10148","APP_TDS","应用程序层的协议","数据库协议",,"1"
"10149","APP_MSSQL","SQL Server数据库","数据库协议",,"1"
"10152","APP_NDPI_CITRIX","Citrix协议","远程控制",,"1"
"10154","APP_H323","H323音频","音视频协议",,"1"
"10158","APP_MSN","MSN","即时通讯",,"1"
"10159","APP_RTMP","RTMP视频","音视频协议",,"1"
"10161","APP_SKINNY","SCCP音频","音视频协议",,"1"
"10162","APP_NFS","网络文件系统","文件共享",,"1"
"10165","APP_MYSQL","MySQL数据库","数据库协议",,"1"
"10171","APP_IAX","IAX音频","音视频协议",,"1"
"10172","APP_RADMIN","RemoteAdmin远程控制","远程控制",,"1"
"10180","APP_POSTGRES","PostgreSQL数据库","数据库协议",,"1"
"10184","APP_VNC","虚拟网络控制台","远程控制",,"1"
"10188","APP_TEAMVIEWER","TeamViewer远程控制","远程控制",,"1"
"10189","APP_XDMCP","显示监控协议","音视频协议",,"1"
"10201","APP_ANCP","接入节点控制协议","基础协议",,"1"
"10205","APP_TOR","洋葱网络","匿名网络",,"1"
"10208","APP_TEAMSPEAK","TeamSpeak语音通讯","音视频协议",,"1"
"10209","APP_ENIP","新一代业务平台","接入管理",,"1"
"10213","APP_SOPCAST","SopCast广播","P2P",,"1"
"10215","APP_ORACLE_TNS","Oracle协议","数据库协议",,"1"
"10216","APP_GUILDWARS",,,,"1"
"10217","APP_DOFUS",,,,"1"
"10221","APP_EDONKEY","电驴","P2P",,"1"
"10222","APP_FIESTA",,,,"1"
"10226","APP_FILETOPIA","Filetopia文件共享","文件共享",,"1"
"10231","APP_STEAM",,,,"1"
"10232","APP_SOULSEEK","音乐共享","P2P",,"1"
"10233","APP_FCIP","IP光纤通道","基础协议",,"1"
"10234","APP_TVANTS","TV蚂蚁","P2P",,"1"
"10236","APP_SOCKS4","网络代理-Socks","匿名网络",,"1"
"10237","APP_PANDO","Pando文件共享","文件共享",,"1"
"10240","APP_CORBA",,,,"1"
"10243","APP_MANOLITO","Manolito播放器","音视频协议",,"1"
"10244","APP_PVFS","分布式虚拟文件系统","文件共享",,"1"
"10245","APP_IMESH","文件共享","P2P",,"1"
"10381","APP_NETMAN","网络人远程控制","远程控制",,"1"
"10383","APP_ZATTOO","ZATTOO视频","音视频协议",,"1"
"10386","APP_ZMQ","消息中间件-","传输层协议",,"1"
"10387","APP_MAPLESTORY",,,,"1"
"10388","APP_STUN","STUN","基础协议",,"1"
"10391","APP_AFP","AppleTalk","苹果专属",,"1"
"10392","APP_APPLEJUICE","AppleJuic文件共享","P2P",,"1"
"10393","APP_BITBORRENT","比特流","P2P",,"1"
"10394","APP_SPOTIFY","声田","音视频协议",,"1"
"10395","APP_TVUPLAYER","TVU播放器","音视频协议",,"1"
"10396","APP_OSCAR","即时通讯协议","即时通讯",,"1"
"10397","APP_RSYNC","数据镜像","基础协议",,"1"
"10399","APP_PPSTREAM","PPStream视频","音视频协议",,"1"
"10401","APP_POPO","网易泡泡","即时通讯",,"1"
"10402","APP_SHOUTCAST","ShoutCast电台","音视频协议",,"1"
"10403","APP_STEALTHNET","StealthNet","P2P",,"1"
"10404","APP_LOTUS_NOTES","Lotus办公软件","即时通讯",,"1"
"10405","APP_SOCRATES",,,,"1"
"10406","APP_HTTP_ACTIVESYNC",,,,"1"
"10409","APP_MMS","微软流媒体协议","音视频协议",,"1"
"10413","APP_NTP","网络时间协议","基础协议",,"1"
"10419","APP_NETMAN_NEGOTIATION","网络人远程控制","远程控制",,"1"
"10422","APP_IPSec_ISAKMP","IPSec管理协议","VPN协议",,"1"
"10429","APP_L2TP","L2TP远程控制","VPN协议",,"1"
"10432","APP_RADIUS","Radius认证","认证协议",,"1"
"10435","APP_SSDP","简单服务发现协议","基础协议",,"1"
"10436","APP_HSRP","热备份路由器协议","基础协议",,"1"
"10438","APP_NETFLOW","NetFlow数据交换","基础协议",,"1"
"10439","APP_GTP",,,,"1"
"10440","APP_GTP_MANAGER",,,,"1"
"10443","APP_IPMessage","飞鸽传书","即时通讯",,"1"
"10444","APP_MEGACO","媒体网关。","音视频协议",,"1"
"10445","APP_XBOX",,,,"1"
"10449","APP_Teredo","网络地址转换穿越","基础协议",,"1"
"10454","APP_MDNS","组播DNS","基础协议",,"1"
"10456","APP_LLMNR","链路本地多播名称解析","基础协议",,"1"
"10457","APP_PCANYWHERE","PcAnywhere远程控制","远程控制",,"1"
"10458","APP_SFLOW","sFlow","基础协议",,"1"
"10459","APP_Tencent_OICQ","腾讯QQ","即时通讯",,"1"
"10460","APP_Tencent_MayBe_OICQ",,,,"1"
"10464","APP_Tencent_QQMail","QQ邮箱","网络邮箱",,"1"
"10465","APP_COLLECTD","主机探针-Collect","基础协议",,"1"
"10466","APP_QUAKE",,,,"1"
"10473","APP_NOE",,,,"1"
"10474","APP_NDPI_ARMAGETRON",,,,"1"
"10480","APP_TFTP","简单文件传输协议","文件共享",,"1"
"10481","APP_Bootstrap_Protocol","引导程序协议","基础协议",,"1"
"10482","APP_KONTIKI",,,,"1"
"10486","APP_VIBER","网络电话-Viber","VOIP",,"1"
"10487","APP_PPLIVE","PPLive视频","音视频协议",,"1"
"10492","APP_HALFLIFE2_AND_MODS",,,,"1"
"10497","APP_XT800","快速通远程控制","远程控制",,"1"
"10503","APP_SUNLOGIN","向日葵远程控制","远程控制",,"1"
"10504","APP_NDPI_BATTLEFIELD",,,,"1"
"10508","APP_SKYPE","Skeype","即时通讯",,"1"
"10509","APP_HOPOPTS",,,,"1"
"10510","APP_IGMP","组管理协议","基础协议",,"1"
"10511","APP_GGP","核心网关协议","基础协议",,"1"
"10512","APP_STREAM",,,,"1"
"10513","APP_CBT","基于核心树的组播路由协议","路由协议",,"1"
"10514","APP_BBN_RCC",,,,"1"
"10515","APP_NVPII",,,,"1"
"10516","APP_PUP",,,,"1"
"10517","APP_ARGUS",,,,"1"
"10518","APP_EMCON",,,,"1"
"10519","APP_XNET",,,,"1"
"10520","APP_CHAOS",,,,"1"
"10521","APP_MUX",,,,"1"
"10522","APP_DCNMEAS",,,,"1"
"10523","APP_HMP",,,,"1"
"10524","APP_PRM",,,,"1"
"10525","APP_IDP",,,,"1"
"10526","APP_TRUNK1",,,,"1"
"10527","APP_TRUNK2",,,,"1"
"10528","APP_LEAF1",,,,"1"
"10529","APP_LEAF2",,,,"1"
"10530","APP_IRT",,,,"1"
"10531","APP_BULK",,,,"1"
"10532","APP_MFE_NSP",,,,"1"
"10533","APP_MERIT",,,,"1"
"10534","APP_DCCP",,,,"1"
"10535","APP_3PC",,,,"1"
"10536","APP_IDPR",,,,"1"
"10537","APP_XTP",,,,"1"
"10538","APP_DDP",,,,"1"
"10539","APP_CMTP",,,,"1"
"10540","APP_TPPP",,,,"1"
"10541","APP_IL",,,,"1"
"10542","APP_SDRP",,,,"1"
"10543","APP_ROUTING",,,,"1"
"10544","APP_FRAGMENT",,,,"1"
"10545","APP_IDRP",,,,"1"
"10546","APP_RSVP",,,,"1"
"10548","APP_DSR",,,,"1"
"10549","APP_BNA",,,,"1"
"10550","APP_INSLP",,,,"1"
"10551","APP_SWIPE",,,,"1"
"10552","APP_NHRP",,,,"1"
"10553","APP_MOBILE",,,,"1"
"10554","APP_TLSP",,,,"1"
"10555","APP_SKIP",,,,"1"
"10556","APP_NONE",,,,"1"
"10557","APP_DSTOPTS",,,,"1"
"10558","APP_SHIM6_OL",,,,"1"
"10559","APP_MIP6",,,,"1"
"10560","APP_SATEXPAK",,,,"1"
"10561","APP_KRYPTOLA",,,,"1"
"10562","APP_RVD",,,,"1"
"10563","APP_IPPC",,,,"1"
"10564","APP_SATMON",,,,"1"
"10565","APP_VISA",,,,"1"
"10566","APP_IPCV",,,,"1"
"10567","APP_CPNX",,,,"1"
"10568","APP_CPHB",,,,"1"
"10569","APP_WSN",,,,"1"
"10570","APP_PVP",,,,"1"
"10571","APP_BRSATMON",,,,"1"
"10572","APP_SUNND",,,,"1"
"10573","APP_WBMON",,,,"1"
"10574","APP_WBEXPAK",,,,"1"
"10575","APP_OSI",,,,"1"
"10576","APP_VMTP",,,,"1"
"10577","APP_SVMTP",,,,"1"
"10578","APP_VINES",,,,"1"
"10579","APP_TTP",,,,"1"
"10580","APP_NSFNETIG",,,,"1"
"10581","APP_DGP",,,,"1"
"10582","APP_TCF",,,,"1"
"10583","APP_SPRITE",,,,"1"
"10584","APP_LARP",,,,"1"
"10585","APP_MTP",,,,"1"
"10586","APP_AX25",,,,"1"
"10587","APP_IPINIP",,,,"1"
"10588","APP_MICP",,,,"1"
"10589","APP_SCCCP",,,,"1"
"10590","APP_ETHERIP",,,,"1"
"10591","APP_ENCAP",,,,"1"
"10592","APP_GMTP",,,,"1"
"10593","APP_IFMP",,,,"1"
"10594","APP_PNNI",,,,"1"
"10595","APP_PIM",,,,"1"
"10596","APP_ARIS",,,,"1"
"10597","APP_SCPS",,,,"1"
"10598","APP_QNX",,,,"1"
"10599","APP_AN",,,,"1"
"10600","APP_SNP",,,,"1"
"10601","APP_COMPAQ",,,,"1"
"10602","APP_PGM",,,,"1"
"10603","APP_DDX",,,,"1"
"10604","APP_IATP",,,,"1"
"10605","APP_STP",,,,"1"
"10606","APP_SRP",,,,"1"
"10607","APP_UTI",,,,"1"
"10608","APP_SMP",,,,"1"
"10609","APP_SM",,,,"1"
"10610","APP_PTP",,,,"1"
"10611","APP_ISIS",,,,"1"
"10612","APP_FIRE",,,,"1"
"10613","APP_CRTP",,,,"1"
"10614","APP_CRUDP",,,,"1"
"10615","APP_SSCOPMCE",,,,"1"
"10616","APP_IPLT",,,,"1"
"10617","APP_SPS",,,,"1"
"10618","APP_PIPE",,,,"1"
"10619","APP_SCTP",,,,"1"
"10620","APP_FC",,,,"1"
"10621","APP_MPLS",,,,"1"
"10622","APP_MANET",,,,"1"
"10623","APP_HIP",,,,"1"
"10624","APP_SHIM6",,,,"1"
"10625","APP_WESP",,,,"1"
"10626","APP_ROHC",,,,"1"
"10627","APP_AX4000",,,,"1"
"10628","APP_NCS","以媒体网关控制协议","VOIP",,"1"
"10629","APP_PPTP","PPTP远程控制","VPN协议",,"1"
"10630","APP_IPSEC","IPSec协议","VPN协议",,"1"
"10631","APP_VOIP","VOIP协议","VOIP",,"1"
"10632","APP_HTTPS",,,,"1"
"10633","APP_NNTPS",,,,"1"
"10634","APP_SMTPS",,,,"1"
"10635","APP_IMAPS",,,,"1"
"10636","APP_POP3S",,,,"1"
"10637","APP_HTTP","超文本传输协议","基础协议",,"1"
"10638","APP_SSL","安全套接层","基础协议",,"1"
"10639","APP_RDP","远程桌面协议","远程控制",,"1"
"10640","APP_Simatic_S7","西门子S7协议","工控协议",,"1"
"10641","APP_COTP","面向连接传输协议","工控协议",,"1"
"10650","APP_Modbus",,,,"1"
"10651","APP_GMSSL","国密协议",,,"1"
"10652","APP_FTPS","加密文件传输协议","文件共享",,"1"
"10701","No_Payload","无负载","无负载",,"1"
"10702","TCP_QueryOnly","TCP仅请求","TCP仅请求","客户端发送SYN，服务器无回应","1"
"10703","TCP_PortClose","TCP端口关闭","TCP端口关闭","客户端发送SYN，服务器发送FIN或RST","1"
"10704","TCP_NoPayload","TCP无负载","TCP无负载","握手正常、挥手正常，没有发送负载数据","1"
"10705","TCP_Unknown","TCP未知协议","TCP未知协议","握手正常、挥手正常，有负载数据，协议未知","1"
"10706","TCP_Other","TCP其他协议",,,"1"
"10707","UDP_NoPayload","UDP无负载协议",,,"1"
"10708","UDP_Unknown","UDP未知协议",,,"1"